# 两阶段随机规划目标函数

## 📋 总目标函数

### 期望总成本
```
min E[C_total] = C1(x) + E[C2(x,y*,ξ)]
```

**说明**：
- **E[C_total]**：期望总成本，优化目标
- **C1(x)**：第一阶段投资成本（确定性）
- **E[C2(x,y*,ξ)]**：第二阶段期望运行成本（随机性）
- **x**：第一阶段决策变量（设备配置）
- **y***：第二阶段最优决策变量（运行调度）
- **ξ**：随机参数（8个残差功率需求场景）

---

## 🏗️ 第一阶段成本 C1(x)

### 数学表达式
```
C1(x) = [(Q_bat × 150 + Q_sc × 200) + (P_bat × 80 + P_sc × 50)] × CRF
```

### 资本回收因子 (CRF)
```
CRF = r × (1 + r)^n / [(1 + r)^n - 1]
    = 0.08 × (1.08)^20 / [(1.08)^20 - 1] 
    ≈ 0.1019
```

### 成本组成
| 项目 | 单价 | 说明 |
|------|------|------|
| **容量成本** | | |
| 电池容量 | 150 $/kWh | Q_bat × 150 |
| 超级电容容量 | 200 $/kWh | Q_sc × 200 |
| **功率成本** | | |
| 电池功率设备 | 80 $/kW | P_bat × 80 |
| 超级电容功率设备 | 50 $/kW | P_sc × 50 |

### 财务参数
- **折现率 (r)**：8%
- **项目生命周期 (n)**：20年
- **CRF**：0.1019（考虑货币时间价值）

---

## ⚡ 第二阶段期望成本 E[C2(x,y*,ξ)]

### 数学表达式
```
E[C2(x,y*,ξ)] = Σ(s=1 to 8) π_s × [Σ(t=1 to 168) |P_bat,s(t)| × 0.02 + Σ(t=1 to 168) |P_sc,s(t)| × 0.01] × (365/7)
```

### 期望值计算
- **Σ(s=1 to 8) π_s × [...]**：对8个场景进行概率加权求和
- **π_s**：场景s的出现概率

### 单个场景运行成本
| 设备类型 | 运行成本 | 计算公式 |
|----------|----------|----------|
| 电池 | 0.02 $/kWh | Σ(t=1 to 168) \|P_bat,s(t)\| × 0.02 |
| 超级电容 | 0.01 $/kWh | Σ(t=1 to 168) \|P_sc,s(t)\| × 0.01 |

### 时间参数
- **168小时**：7天 × 24小时 = 168小时
- **年化因子**：365/7 ≈ 52.14（将7天结果推广到全年）

### 决策变量
- **P_bat,s(t), P_sc,s(t)**：场景s下第t小时的最优充放电功率
- **|P_bat,s(t)|**：功率绝对值，表示充放电能量吞吐量

---

## 📊 关键参数总结

### 决策变量
| 阶段 | 变量 | 维度 | 说明 |
|------|------|------|------|
| 第一阶段 | Q_bat, Q_sc | 标量 | 电池、超级电容容量 (kWh) |
| 第一阶段 | P_bat, P_sc | 标量 | 电池、超级电容功率 (kW) |
| 第二阶段 | P_bat,s(t) | 8×168 | 每个场景每小时电池功率 |
| 第二阶段 | P_sc,s(t) | 8×168 | 每个场景每小时超级电容功率 |

### 成本参数
| 参数 | 数值 | 单位 | 说明 |
|------|------|------|------|
| 电池容量成本 | 150 | $/kWh | 电池储能容量投资 |
| 超级电容容量成本 | 200 | $/kWh | 超级电容储能容量投资 |
| 电池功率成本 | 80 | $/kW | 电池功率设备投资 |
| 超级电容功率成本 | 50 | $/kW | 超级电容功率设备投资 |
| 电池运行成本 | 0.02 | $/kWh | 电池充放电运行成本 |
| 超级电容运行成本 | 0.01 | $/kWh | 超级电容充放电运行成本 |

### 财务参数
| 参数 | 数值 | 说明 |
|------|------|------|
| 折现率 | 8% | 资金成本 |
| 项目生命周期 | 20年 | 设备使用年限 |
| 资本回收因子 | 0.1019 | 年化投资成本系数 |

---

## 💡 经济学意义

### 第一阶段成本特点
- **确定性**：投资决策在不确定性实现前做出
- **一次性**：设备配置决策，影响整个项目生命周期
- **资金时间价值**：使用CRF考虑货币贬值和机会成本

### 第二阶段成本特点
- **随机性**：运行成本取决于未来的功率需求场景
- **期望优化**：考虑所有可能场景的概率加权平均
- **递归决策**：每个场景下独立优化运行调度

### CRF vs 简化方法对比
| 方法 | 年化系数 | 经济学含义 |
|------|----------|------------|
| 简化方法 | 1/20 = 0.05 | 忽略货币时间价值 |
| CRF方法 | 0.1019 | 考虑8%资金成本 |
| **差异** | **+103.8%** | **更准确的财务分析** |

---

## 🎯 模型特点

1. **标准两阶段随机规划**：符合学术界严格定义
2. **时间分离**：投资决策先于运行决策
3. **信息结构**：第一阶段决策在不确定性实现前做出
4. **期望优化**：最小化所有场景的期望总成本
5. **财务准确性**：使用年金因子考虑货币时间价值
