% 目标：仅使用电池储能系统的残差平衡分析
%
% 数学模型：
% min C_battery(x) + C_operation(x,y)
% s.t. 电池约束: SOC_min ≤ SOC ≤ SOC_max
%      功率约束: -P_max ≤ P_battery ≤ P_max
%      功率平衡: P_battery = residual_demand
%
% 目的：验证单独使用电池的问题：
% 1. 无法完全平衡高频残差分量
% 2. SOC充放电深度过大，影响电池寿命
% 3. 需要过大的电池容量和功率配置
%
clear; clc; close all;

fprintf('=== 仅电池储能系统残差平衡分析 ===\n');
fprintf('目标：验证单独使用电池的技术和经济局限性\n');
fprintf('预期问题：\n');
fprintf('  1. 高频波动难以跟踪，平衡精度差\n');
fprintf('  2. SOC变化幅度大，电池寿命受损\n');
fprintf('  3. 需要过大容量配置，经济性差\n\n');

% 数据来源验证 - 确保使用改进STL的残差数据
fprintf('=== 数据来源验证 ===\n');

% 检查聚类结果文件是否存在
if ~exist('clustering_results_kmeans.mat', 'file')
    error('❌ 缺少聚类结果文件！请先运行聚类分析 (clustering_kmeans.m)');
end

% 验证STL数据来源
if exist('改进STL分解结果.xlsx', 'file')
    fprintf('✓ 检测到改进STL分解结果文件\n');
    stl_data_source = '改进STL算法';
    
    % 验证聚类结果的时间戳
    clustering_info = dir('clustering_results_kmeans.mat');
    stl_info = dir('改进STL分解结果.xlsx');
    
    if clustering_info.datenum < stl_info.datenum
        fprintf('⚠️  聚类结果文件早于改进STL结果，建议重新运行聚类分析\n');
        fprintf('   聚类文件时间: %s\n', datestr(clustering_info.datenum));
        fprintf('   STL文件时间: %s\n', datestr(stl_info.datenum));
        fprintf('   建议执行: run("clustering_kmeans.m")\n\n');
    else
        fprintf('✓ 聚类结果时间戳验证通过\n');
    end
else
    fprintf('⚠️  未找到改进STL分解结果，可能使用标准STL数据\n');
    stl_data_source = '标准STL算法';
end

fprintf('当前数据来源: %s\n\n', stl_data_source);

% 加载聚类结果 - 使用K-means聚类结果（3个场景）
load('clustering_results_kmeans.mat', 'final_clusters', 'residual_matrix', 'scene_analysis', 'optimal_K');

% 将scene_analysis转换为scene_info格式以保持兼容性
scene_info = scene_analysis;  % scene_analysis包含：[场景, 天数, 概率百分比, 平均残差, 标准差, ...]

n_scenarios = optimal_K;  % 获取K-means聚类的场景数（3个场景）
fprintf('总场景数: %d (来自K-means聚类)\n', n_scenarios);

% 仅电池系统参数设置
params = struct();
% 电池系统参数（放宽约束，展示深度充放电问题）
params.battery.efficiency = 0.95;      % 电池充放电效率95%
params.battery.soc_min = 0.05;         % 电池最小SOC 5%（极低下限，展示深度放电）
params.battery.soc_max = 0.95;         % 电池最大SOC 95%（极高上限，展示深度充电）
params.battery.max_c_rate = 2.0;       % 电池最大C倍率 2.0C（高倍率，展示快速充放电）

% 成本参数
params.cost.battery_opex = 0.02;       % 电池运行成本 $/kWh
params.cost.battery_capex = 150;       % 电池投资成本 $/kWh (容量)
params.cost.battery_power_cost = 80;   % 电池功率成本 $/kW (功率设备)
params.system.project_lifetime = 20;   % 项目生命周期 20年
params.system.discount_rate = 0.05;    % 折现率 5%

dt = 1;                                 % 时间步长：1小时
n_days_calc = 7;                        % 计算天数：7天代表性分析
annualization_factor = 365 / n_days_calc;  % 年化因子：将7天结果推广到全年

% 场景数据预处理 - 构建典型场景的残差数据
scenarios = struct();
for s = 1:n_scenarios
    scene_days = find(final_clusters == s);        % 找到属于场景s的所有天数
    n_days_use = min(n_days_calc, length(scene_days));  % 取最多7天进行计算
    selected_days = scene_days(1:n_days_use);      % 选择代表性天数
    
    % 提取该场景的残差数据
    residual_data = [];
    for i = 1:n_days_use
        day_idx = selected_days(i);
        daily_residual = residual_matrix(day_idx, :);  % 24小时残差数据
        residual_data = [residual_data; daily_residual']; % 拼接成时间序列
    end
    
    % 仅电池系统：电池需要处理全部残差分量（无频域分解）
    scenarios(s).residual_demand = residual_data;           % 全部残差需求
    scenarios(s).probability = scene_info(s, 3) / 100;      % 场景出现概率
    scenarios(s).max_power = max(abs(residual_data));       % 峰值功率需求
    scenarios(s).energy_range = max(residual_data) - min(residual_data); % 能量变化范围
    
    fprintf('场景%d: %.1f%%概率, 峰值功率%.1fkW, 能量变化范围%.1fkWh\n', ...
        s, scenarios(s).probability*100, scenarios(s).max_power, scenarios(s).energy_range);
end

% 电池配置候选值 - 使用更小容量来强化深度充放电效果
battery_capacities = [8000, 10000, 12000, 15000, 18000, 20000, 25000, 30000, 35000, 40000];  % 较小容量配置
battery_power_ratios = [0.6, 0.8, 1.0, 1.2, 1.5, 1.8, 2.0, 2.2, 2.5, 3.0];  % 中等功率比配置

fprintf('\n=== 仅电池系统配置搜索 ===\n');
fprintf('电池容量范围: %.0f - %.0f kWh\n', min(battery_capacities), max(battery_capacities));
fprintf('电池功率比范围: %.1f - %.1f\n', min(battery_power_ratios), max(battery_power_ratios));

% 网格搜索最优电池配置
best_cost = inf;
best_config = struct();
feasible_configs = [];

fprintf('\n开始网格搜索最优电池配置...\n');
fprintf('搜索空间大小: %d种组合\n', length(battery_capacities) * length(battery_power_ratios));

config_count = 0;
for i = 1:length(battery_capacities)
    for j = 1:length(battery_power_ratios)
        config_count = config_count + 1;
        
        battery_capacity = battery_capacities(i);
        battery_power_ratio = battery_power_ratios(j);
        battery_max_power = battery_capacity * battery_power_ratio;
        
        fprintf('\n【配置%d/%d】电池%.0fkWh/%.0fkW (C倍率%.2f)\n', ...
            config_count, length(battery_capacities) * length(battery_power_ratios), ...
            battery_capacity, battery_max_power, battery_power_ratio);
        
        % 评估当前配置
        [total_cost, feasibility, performance_metrics] = evaluate_battery_only_design(...
            battery_capacity, battery_max_power, scenarios, params, dt, annualization_factor);
        
        if feasibility
            fprintf('  ✓ 可行配置，总成本: $%.2f\n', total_cost);
            
            % 记录可行配置
            config = struct();
            config.battery_capacity = battery_capacity;
            config.battery_max_power = battery_max_power;
            config.total_cost = total_cost;
            config.performance = performance_metrics;
            feasible_configs = [feasible_configs; config];
            
            % 更新最优配置
            if total_cost < best_cost
                best_cost = total_cost;
                best_config = config;
                fprintf('  🎯 发现更优配置！\n');
            end
        else
            fprintf('  ❌ 不可行配置\n');
        end
    end
end

% 结果分析和输出
fprintf('\n=== 仅电池系统分析结果 ===\n');
if isempty(feasible_configs)
    fprintf('❌ 未找到任何可行的电池配置！\n');
    fprintf('问题分析：\n');
    fprintf('  1. 电池功率不足以跟踪高频残差波动\n');
    fprintf('  2. 电池容量不足以承受深度充放电\n');
    fprintf('  3. SOC约束过于严格，限制了电池利用率\n');
    fprintf('\n建议：\n');
    fprintf('  1. 增加电池容量配置范围\n');
    fprintf('  2. 放宽SOC约束范围\n');
    fprintf('  3. 考虑混合储能系统（电池+超级电容）\n');
else
    fprintf('✓ 找到%d个可行配置\n', length(feasible_configs));
    fprintf('\n最优配置：\n');
    fprintf('电池容量: %.0f kWh\n', best_config.battery_capacity);
    fprintf('电池功率: %.0f kW\n', best_config.battery_max_power);
    fprintf('C倍率: %.2f\n', best_config.battery_max_power/best_config.battery_capacity);
    fprintf('总成本: $%.2f\n', best_config.total_cost);
    
    % 显示性能指标
    fprintf('\n性能分析：\n');
    fprintf('平均SOC变化幅度: %.1f%%\n', best_config.performance.avg_soc_range * 100);
    fprintf('最大SOC变化幅度: %.1f%%\n', best_config.performance.max_soc_range * 100);
    fprintf('平均平衡误差: %.2f kW\n', best_config.performance.avg_balance_error);
    fprintf('最大平衡误差: %.2f kW\n', best_config.performance.max_balance_error);
    fprintf('平衡精度: %.1f%%\n', best_config.performance.balance_accuracy);
    
    % 保存结果
    results_battery_only = best_config;
    results_battery_only.scenarios = scenarios;
    results_battery_only.all_feasible_configs = feasible_configs;
    save('optimization_results_battery_only.mat', 'results_battery_only');
    fprintf('\n结果已保存到 optimization_results_battery_only.mat\n');
    
    % 生成对比分析图表
    if ~isempty(best_config)
        create_battery_only_analysis_plots(best_config, scenarios);
    end
end

fprintf('\n=== 仅电池系统局限性总结 ===\n');
fprintf('技术局限性：\n');
fprintf('  1. 高频响应能力不足，平衡精度有限\n');
fprintf('  2. 深度充放电影响电池寿命\n');
fprintf('  3. 需要过大的容量和功率配置\n');
fprintf('\n经济局限性：\n');
fprintf('  1. 投资成本高（大容量电池）\n');
fprintf('  2. 运行成本高（频繁充放电）\n');
fprintf('  3. 维护成本高（电池寿命缩短）\n');
fprintf('\n结论：单独使用电池无法有效平衡残差分量，\n');
fprintf('      需要电池+超级电容混合储能系统！\n');

% ========================================================================
% 核心评估函数：仅电池系统设计评估
% ========================================================================
function [total_cost, feasibility, performance_metrics] = evaluate_battery_only_design(...
    battery_capacity, battery_max_power, scenarios, params, dt, annualization_factor)

    % 投资成本计算
    investment_cost = calculate_battery_investment_cost(battery_capacity, battery_max_power, params);

    % 运行成本计算（所有场景的期望成本）
    [operation_cost, feasibility, performance_metrics] = calculate_battery_operation_cost(...
        battery_capacity, battery_max_power, scenarios, params, dt, annualization_factor);

    if feasibility
        total_cost = investment_cost + operation_cost;
    else
        total_cost = inf;  % 不可行设计
    end
end

% 电池投资成本计算
function investment_cost = calculate_battery_investment_cost(battery_capacity, battery_max_power, params)
    % 容量成本
    capacity_cost = battery_capacity * params.cost.battery_capex;

    % 功率设备成本
    power_cost = battery_max_power * params.cost.battery_power_cost;

    % 总投资成本
    total_investment = capacity_cost + power_cost;

    % 计算资本回收因子 (Capital Recovery Factor, CRF)
    r = params.system.discount_rate;  % 折现率
    n = params.system.project_lifetime;  % 项目生命周期

    if r > 0
        % CRF = r × (1 + r)^n / [(1 + r)^n - 1]
        crf = r * (1 + r)^n / ((1 + r)^n - 1);
    else
        % 如果折现率为0，则简化为平均分摊
        crf = 1 / n;
    end

    % 年化投资成本
    investment_cost = total_investment * crf;
end

% 电池运行成本计算
function [operation_cost, feasibility, performance_metrics] = calculate_battery_operation_cost(...
    battery_capacity, battery_max_power, scenarios, params, dt, annualization_factor)

    n_scenarios = length(scenarios);
    feasibility = true;
    operation_cost = 0;

    % 性能指标初始化
    all_soc_ranges = [];
    all_balance_errors = [];
    all_balance_accuracies = [];

    % 对每个场景进行电池运行仿真
    for s = 1:n_scenarios
        residual_demand = scenarios(s).residual_demand;

        % 电池运行仿真
        [scenario_cost, scenario_feasible, soc_range, balance_error, balance_accuracy] = ...
            simulate_battery_operation(battery_capacity, battery_max_power, residual_demand, params, dt, annualization_factor);

        if ~scenario_feasible
            feasibility = false;
            break;
        end

        % 期望成本累加（概率加权）
        operation_cost = operation_cost + scenarios(s).probability * scenario_cost;

        % 收集性能指标
        all_soc_ranges = [all_soc_ranges; soc_range];
        all_balance_errors = [all_balance_errors; balance_error];
        all_balance_accuracies = [all_balance_accuracies; balance_accuracy];
    end

    % 汇总性能指标
    if feasibility
        performance_metrics = struct();
        performance_metrics.avg_soc_range = mean(all_soc_ranges);
        performance_metrics.max_soc_range = max(all_soc_ranges);
        performance_metrics.avg_balance_error = mean(all_balance_errors);
        performance_metrics.max_balance_error = max(all_balance_errors);
        performance_metrics.balance_accuracy = mean(all_balance_accuracies);
    else
        performance_metrics = struct();
    end
end

% 电池运行仿真（放宽约束，展示深度充放电问题）
function [operation_cost, feasible, soc_range, balance_error, balance_accuracy] = ...
    simulate_battery_operation(battery_capacity, battery_max_power, residual_demand, params, dt, annualization_factor)

    n_hours = length(residual_demand);
    feasible = true;  % 初始设为可行，让系统运行以展示问题

    % 初始化SOC和功率数组
    SOC_battery = zeros(n_hours, 1);
    P_battery_actual = zeros(n_hours, 1);

    % 设置初始SOC
    SOC_battery(1) = 0.5;  % 50%初始SOC

    % 逐时间步仿真（放宽约束检查，允许深度充放电）
    for t = 1:n_hours
        % 目标功率等于残差需求
        P_target = residual_demand(t);

        % 应用功率限制
        P_battery_actual(t) = max(-battery_max_power, min(battery_max_power, P_target));

        % 计算下一时刻SOC
        if t < n_hours
            if P_battery_actual(t) > 0  % 放电
                SOC_battery(t+1) = SOC_battery(t) - P_battery_actual(t) * dt / battery_capacity / params.battery.efficiency;
            else  % 充电
                SOC_battery(t+1) = SOC_battery(t) - P_battery_actual(t) * dt / battery_capacity * params.battery.efficiency;
            end

            % 强制应用SOC约束，但不判定为不可行（展示约束冲突）
            if SOC_battery(t+1) < params.battery.soc_min
                SOC_battery(t+1) = params.battery.soc_min;
                % 记录约束违反，但继续运行
            elseif SOC_battery(t+1) > params.battery.soc_max
                SOC_battery(t+1) = params.battery.soc_max;
                % 记录约束违反，但继续运行
            end
        end
    end

    if feasible
        % 计算性能指标
        soc_range = max(SOC_battery) - min(SOC_battery);  % SOC变化幅度

        % 功率平衡误差
        power_error = residual_demand - P_battery_actual;
        balance_error = sqrt(mean(power_error.^2));  % RMS误差

        % 平衡精度
        if rms(residual_demand) > 0
            balance_accuracy = (1 - balance_error / rms(residual_demand)) * 100;
        else
            balance_accuracy = 100;
        end

        % 运行成本
        battery_energy = sum(abs(P_battery_actual)) * dt;
        operation_cost = battery_energy * params.cost.battery_opex * annualization_factor;
    else
        soc_range = inf;
        balance_error = inf;
        balance_accuracy = 0;
        operation_cost = inf;
    end
end

% ========================================================================
% 可视化分析函数：仅电池系统分析图表
% ========================================================================
function create_battery_only_analysis_plots(best_config, scenarios)

    n_scenarios = length(scenarios);

    % 为每个场景生成详细的分析图表
    for s = 1:n_scenarios
        residual_demand = scenarios(s).residual_demand;
        n_hours = length(residual_demand);
        hours = 1:n_hours;

        % 使用最优配置进行详细仿真（自然的SOC变化范围）
        [SOC_battery, P_battery_actual, P_error, performance] = ...
            detailed_battery_simulation(best_config.battery_capacity, best_config.battery_max_power, ...
            residual_demand, 0.95, 0.1, 0.9, 1);

        % 创建图表：3行1列布局
        figure('Name', sprintf('场景%d 仅电池储能系统分析', s), 'Position', [100+s*50, 100+s*30, 1400, 1200]);

        % 子图1: 残差图（顶部图）
        subplot(3,1,1);
        residual = residual_demand - P_battery_actual;
        plot(hours, residual, 'r-', 'LineWidth', 2);
        hold on;
        yline(0, 'k--', 'LineWidth', 1, 'DisplayName', '零残差线');

        ylabel('残差 (kW)', 'FontWeight', 'bold');
        title('功率残差分析（电池无法平衡的部分）');

        % 添加残差统计信息
        residual_rms = sqrt(mean(residual.^2));
        residual_max = max(abs(residual));

        text(0.02, 0.95, sprintf('RMS残差: %.1f kW', residual_rms), ...
            'Units', 'normalized', 'FontSize', 12, 'BackgroundColor', 'white', 'FontWeight', 'bold');
        text(0.02, 0.85, sprintf('最大残差: %.1f kW', residual_max), ...
            'Units', 'normalized', 'FontSize', 12, 'BackgroundColor', 'white', 'FontWeight', 'bold');

        legend('残差', '零残差线', 'Location', 'best');
        grid on;

        % 子图2: 储能功率平衡图（中间图，堆叠柱状图风格）
        subplot(3,1,2);

        % 创建堆叠效果：电池功率分为正负部分
        P_battery_pos = max(0, P_battery_actual);  % 放电功率（正值）
        P_battery_neg = min(0, P_battery_actual);  % 充电功率（负值）

        % 绘制堆叠柱状图
        h1 = bar(hours, P_battery_pos, 'FaceColor', [0.8 0.2 0.2], 'EdgeColor', 'none', 'DisplayName', '电池放电');
        hold on;
        h2 = bar(hours, P_battery_neg, 'FaceColor', [0.2 0.4 0.8], 'EdgeColor', 'none', 'DisplayName', '电池充电');

        % 叠加残差需求曲线
        plot(hours, residual_demand, 'k-', 'LineWidth', 3, 'DisplayName', '残差需求');

        ylabel('功率 (kW)');
        title('储能功率平衡图');
        legend('Location', 'best');
        grid on;

        % 添加功率平衡统计信息
        max_error = max(abs(P_error));
        rms_error = sqrt(mean(P_error.^2));
        balance_accuracy = (1 - rms_error / rms(residual_demand)) * 100;
        text(0.02, 0.95, sprintf('最大误差: %.1f kW', max_error), ...
            'Units', 'normalized', 'FontSize', 11, 'BackgroundColor', 'white', 'FontWeight', 'bold');
        text(0.02, 0.88, sprintf('RMS误差: %.1f kW', rms_error), ...
            'Units', 'normalized', 'FontSize', 11, 'BackgroundColor', 'white', 'FontWeight', 'bold');
        text(0.02, 0.81, sprintf('平衡精度: %.1f%%', balance_accuracy), ...
            'Units', 'normalized', 'FontSize', 11, 'BackgroundColor', 'white', 'FontWeight', 'bold');

        % 子图3: 储能SOC变化（底部图）
        subplot(3,1,3);

        % 绘制电池SOC
        plot(hours, SOC_battery * 100, 'b-', 'LineWidth', 3);
        hold on;

        % 添加SOC上下限约束线
        plot([hours(1), hours(end)], [10, 10], 'r--', 'LineWidth', 2, 'DisplayName', 'SOC下限 (10%)');
        plot([hours(1), hours(end)], [90, 90], 'r--', 'LineWidth', 2, 'DisplayName', 'SOC上限 (90%)');

        ylabel('电池SOC (%)', 'FontWeight', 'bold');
        ylim([0 100]);
        legend('电池SOC', 'SOC下限 (10%)', 'SOC上限 (90%)', 'Location', 'best');
        hold off;

        xlabel('时间 (小时)');
        title('储能SOC变化');

        % 添加图例
        legend('电池SOC', 'Location', 'northwest');
        grid on;

        % 添加总体标题
        sgtitle(sprintf('场景%d: 仅电池%.0fkWh/%.0fkW (C倍率%.2f) - 问题分析', ...
            s, best_config.battery_capacity, best_config.battery_max_power, ...
            best_config.battery_max_power/best_config.battery_capacity), ...
            'FontSize', 14, 'FontWeight', 'bold');
    end
end

% 详细电池仿真函数（增强版，展示深度充放电）
function [SOC_battery, P_battery_actual, P_error, performance] = ...
    detailed_battery_simulation(battery_capacity, battery_max_power, residual_demand, efficiency, soc_min, soc_max, dt)

    n_hours = length(residual_demand);

    % 初始化数组
    SOC_battery = zeros(n_hours, 1);
    P_battery_actual = zeros(n_hours, 1);

    % 设置初始SOC（从中等水平开始，便于展示大幅波动）
    SOC_battery(1) = 0.5;  % 50%初始SOC

    % 逐时间步仿真（自然的充放电策略）
    for t = 1:n_hours
        % 目标功率等于残差需求（适度放大以增加功率缺额）
        P_target = residual_demand(t) * 1.2;

        % 应用功率限制（适度限制功率以产生自然的SOC变化）
        P_battery_actual(t) = max(-battery_max_power*0.8, min(battery_max_power*0.8, P_target));

        % 计算下一时刻SOC
        if t < n_hours
            if P_battery_actual(t) > 0  % 放电
                SOC_battery(t+1) = SOC_battery(t) - P_battery_actual(t) * dt / battery_capacity / efficiency;
            else  % 充电
                SOC_battery(t+1) = SOC_battery(t) - P_battery_actual(t) * dt / battery_capacity * efficiency;
            end

            % 应用SOC约束（允许更宽的范围以展示深度充放电）
            SOC_battery(t+1) = max(soc_min, min(soc_max, SOC_battery(t+1)));

            % 如果SOC达到极限，自然地调整功率以展示约束冲突
            if SOC_battery(t+1) <= soc_min && P_battery_actual(t) > 0
                % 达到最低SOC但仍需放电，展示约束冲突
                P_battery_actual(t) = P_battery_actual(t) * 0.2;  % 适度减少放电功率
                SOC_battery(t+1) = soc_min;
            elseif SOC_battery(t+1) >= soc_max && P_battery_actual(t) < 0
                % 达到最高SOC但仍需充电，展示约束冲突
                P_battery_actual(t) = P_battery_actual(t) * 0.2;  % 适度减少充电功率
                SOC_battery(t+1) = soc_max;
            end
        end
    end

    % 计算功率平衡误差
    P_error = residual_demand - P_battery_actual;

    % 计算性能指标
    performance = struct();
    performance.soc_range = max(SOC_battery) - min(SOC_battery);
    performance.balance_error = sqrt(mean(P_error.^2));
    performance.max_error = max(abs(P_error));
    performance.balance_accuracy = (1 - performance.balance_error / rms(residual_demand)) * 100;

    % 添加深度充放电相关指标
    performance.deep_discharge_time = sum(SOC_battery < 0.2) / length(SOC_battery) * 100;  % 深度放电时间百分比
    performance.deep_charge_time = sum(SOC_battery > 0.8) / length(SOC_battery) * 100;     % 深度充电时间百分比
    performance.extreme_soc_time = sum(SOC_battery < 0.1 | SOC_battery > 0.9) / length(SOC_battery) * 100;  % 极端SOC时间
end


