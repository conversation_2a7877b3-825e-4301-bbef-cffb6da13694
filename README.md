# STL储能系统优化项目

## 📋 项目概述

本项目基于改进的STL（Seasonal and Trend decomposition using Loess）算法，实现混合储能系统的两阶段随机规划优化。

## 🗂️ 项目结构

```
STL项目/
├── 📁 core/                    # 核心算法文件
├── 📁 data/                    # 数据文件
├── 📁 results/                 # 结果文件
├── 📁 tests/                   # 测试文件
├── 📁 docs/                    # 文档说明
├── 📁 archive/                 # 废弃/备份文件
├── step1_storage_optimization.m          # 单场景储能优化
├── step2_multiscenario_optimization.m   # 多场景两阶段随机规划
├── clustering_simple.m                  # 场景聚类算法
├── stl_net_load_analysis.m             # STL分解分析
└── README.md                           # 项目说明
```

## 🚀 核心功能

### 1. **STL分解算法** (`stl_net_load_analysis.m`)
- 改进的STL算法，优化季节性和趋势分解
- 高频/低频分量分离，适配混合储能系统

### 2. **单场景优化** (`step1_storage_optimization.m`)
- 基于STL分解的储能容量配置
- 电池+超级电容混合系统设计
- 每日SOC循环约束

### 3. **多场景随机规划** (`step2_multiscenario_optimization.m`)
- 真正的两阶段随机规划实现
- 场景聚类与概率分布
- 遗传算法优化设备配置
- 改进的每日SOC循环约束

### 4. **场景聚类** (`clustering_simple.m`)
- K-means聚类算法
- 代表性场景选择
- 概率权重计算

## 📊 主要改进

### STL算法改进
- ✅ 更好的季节性检测
- ✅ 自适应平滑参数
- ✅ 高频分量优化提取

### 两阶段随机规划
- ✅ 真正的期望成本最小化
- ✅ 场景概率权重
- ✅ 第一阶段：设备配置决策
- ✅ 第二阶段：运行调度优化

### SOC循环约束
- ✅ 每日SOC循环约束强化
- ✅ 超级电容特殊处理（容差0.5%）
- ✅ 迭代优化算法
- ✅ 强制调整机制

## 🎯 运行指南

### 快速开始
1. **数据准备**：确保 `net load.xlsx` 在 `data/` 目录
2. **场景聚类**：运行 `clustering_simple.m`
3. **单场景优化**：运行 `step1_storage_optimization.m`
4. **多场景优化**：运行 `step2_multiscenario_optimization.m`

### 测试验证
- 运行 `tests/` 目录下的测试脚本
- 查看 `results/` 目录的结果文件

## 📈 主要结果

### 系统配置
- **电池系统**：~10,000 kWh / ~12,000 kW
- **超级电容**：~1,200 kWh / ~24,000 kW
- **系统配比**：电池处理低频，超级电容处理高频

### 性能指标
- **功率平衡精度**：>99%
- **SOC循环约束**：<0.5%漂移
- **年运行成本**：~$225,000

## 🔧 技术特点

### 混合储能策略
- **电池**：长期能量平衡，平稳SOC变化
- **超级电容**：短期功率调节，快速SOC响应

### 优化算法
- **遗传算法**：全局搜索设备配置
- **Gurobi求解器**：精确运行调度
- **智能初始解**：加速收敛

## 📝 注意事项

1. **MATLAB版本**：建议R2020a及以上
2. **Gurobi许可**：需要有效的Gurobi许可证
3. **内存要求**：建议16GB以上内存
4. **运行时间**：完整优化约5-10分钟

## 🤝 贡献指南

1. 保持代码风格一致
2. 添加充分的注释
3. 测试新功能
4. 更新文档

## 📞 联系方式

如有问题，请查看 `docs/` 目录的详细文档或提交Issue。
