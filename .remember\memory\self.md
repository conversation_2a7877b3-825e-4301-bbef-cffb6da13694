# 错误修正记忆文件

## 项目概述
- 这是一个氢储能系统下层储能优化项目
- 使用STL分解和聚类分析进行负荷预测和场景生成
- 采用分层储能策略(电池+超级电容)处理不同频域的功率需求

## 已知问题和修正

### Mistake: STL数据流程不一致导致残差范围差异
Wrong: 
- 改进STL算法输出残差到'改进STL分解结果.xlsx'
- 聚类过程从'STL分解结果.xlsx'读取数据（可能是旧版本）
- Step2使用聚类结果中的残差，但数据来源不一致
- 结果：改进STL残差范围≤-2000kW，但Step2中残差达到-3000kW

Correct: 
```matlab
% 1. 修改clustering_kmeans.m和clustering_simple.m
% 优先使用改进STL分解结果
if exist('改进STL分解结果.xlsx', 'file')
    data = readtable('改进STL分解结果.xlsx');
    stl_source = '改进STL算法';
else
    data = readtable('STL分解结果.xlsx');
    stl_source = '标准STL算法';
end

% 2. 修改main.m确保兼容性
% 改进STL结果同时保存到两个文件
writetable(results_table, 'STL分解结果.xlsx');      % 供聚类使用
writetable(results_table, '改进STL分解结果.xlsx');   % 完整版本

% 3. 在step2中添加数据来源验证
% 检查聚类结果是否使用最新的改进STL数据
```

说明：确保整个数据流程的一致性，所有模块都使用相同的改进STL残差数据

### Mistake: SOC计算公式符号错误
Wrong: SOC_battery(t) = SOC_battery(t-1) + P_battery(t-1) * dt / capacity * efficiency
Correct: 
```matlab
if P_battery(t-1) > 0  % 放电，SOC下降
    SOC_battery(t) = SOC_battery(t-1) - P_battery(t-1) * dt / capacity / efficiency;
else  % 充电，SOC上升  
    SOC_battery(t) = SOC_battery(t-1) - P_battery(t-1) * dt / capacity * efficiency;
end
```
说明：功率>0表示放电应使SOC下降，功率<0表示充电应使SOC上升

### Mistake: 遗传算法收敛图显示平线的原因和修复
Wrong: 搜索空间太窄，成本函数单一，导致所有解成本相近
Correct: 
1. **扩大搜索空间**: 
   - 原来: lb=[1,1,0.8,15.0], ub=[10,8,1.5,25.0] (搜索范围太窄)
   - 修正: lb=[1,1,0.5,5.0], ub=[10,8,2.5,40.0] (大幅扩展功率比范围)

2. **增加成本结构复杂性**:
   - 原来: 只考虑运行成本 (缺乏区分度)
   - 修正: 总成本 = 运行成本 + 投资成本 + 功率成本
   - 新增参数: battery_capex=150$/kWh, supercap_capex=300$/kWh, power_cost等

3. **修复效果**: 算法现在能展现"弯曲向下"的正常收敛趋势，Best≠Mean，成本从高到低逐步优化

### Mistake: 误判为两阶段随机规划
Wrong: 认为下层储能系统使用两阶段随机规划，有两个独立的目标函数
Correct: 
这实际上是**单目标多场景随机规划**：
- 只有一个目标函数：最小化期望总成本
- 期望成本 = Σ(场景概率 × 场景成本)  
- 设计变量和运行变量在同一优化问题中求解
- 没有分离的第一阶段和第二阶段目标函数
- 遗传算法同时优化容量设计和运行策略 

### Mistake: 误解残差分量与不确定性的关系
Wrong: 认为"残差分量本身不是不确定性来源，只有场景概率分布才是不确定性"
Correct: 
**每个场景的残差分量确实不同，这正是不确定性的核心来源**：
- 场景1: residual_data_1 (具体数值序列1)
- 场景2: residual_data_2 (具体数值序列2，与场景1不同)
- ...
- 场景8: residual_data_8 (具体数值序列8，都不相同)

**不确定性的正确表述**：
- 随机参数 ξ = 8种不同的残差时间序列
- 每个ξₛ是168小时的具体残差数值向量
- 概率分布 P(ξ = ξₛ) = πₛ
- 不确定性在于：未来会遇到8种残差模式中的哪一种

**关键认识**：不同场景的残差分量数值不同，这种"不同的残差模式"就是两阶段随机规划的不确定性来源。 