基于K-means的残差场景聚类方法
为了有效处理STL分解后残差序列的不确定性，本研究采用K-means聚类算法对残差数据进行场景划分，以构建两阶段随机规划模型的概率场景集合。K-means算法通过最小化簇内平方和目标函数来实现数据的最优分割，其核心思想是将n个数据点划分为k个簇，使得每个数据点到其所属簇中心的距离平方和最小。算法的目标函数可表示为：

$$J = \sum_{i=1}^{k} \sum_{x \in C_i} ||x - \mu_i||^2$$

其中，$C_i$表示第i个簇，$\mu_i$表示第i个簇的中心，$||x - \mu_i||^2$表示数据点x到簇中心的欧几里得距离平方。

在特征工程阶段，本研究从原始残差时间序列中提取了14个多维特征（更好的聚类），包括基础统计特征（均值、标准差、最大值、最小值、波动范围、中位数）、能量功率特征（总能量、均方根值、峰值因子）、分布特征（偏度、峰度）以及时间特征（早上、下午、晚上时段均值）。为消除不同特征间的量纲差异，采用Z-score标准化方法对特征矩阵进行归一化处理：

$$z_{ij} = \frac{x_{ij} - \bar{x}_j}{\sigma_j}$$

其中，$x_{ij}$表示第i个样本的第j个特征值，$\bar{x}_j$和$\sigma_j$分别表示第j个特征的均值和标准差。

聚类数量的确定采用肘部法则(Elbow Method)，通过计算不同k值下的簇内平方和(Within-Cluster Sum of Squares, WCSS)，寻找WCSS下降速率显著变缓的拐点。WCSS的计算公式为：

$$\text{WCSS}(k) = \sum_{i=1}^{k} \sum_{x \in C_i} ||x - \mu_i||^2$$

肘部法则的数学表达通过计算一阶和二阶差分来量化：

$$\Delta_1(k) = \text{WCSS}(k-1) - \text{WCSS}(k)$$

$$\Delta_2(k) = \Delta_1(k-1) - \Delta_1(k)$$

最优聚类数$k^*$定义为二阶差分最大值对应的k值：

$$k^* = \arg\max_k \Delta_2(k)$$

为验证聚类效果的稳定性，本研究采用轮廓系数(Silhouette Coefficient)作为聚类质量的评价指标。对于样本点i，其轮廓系数定义为：

$$s(i) = \frac{b(i) - a(i)}{\max\{a(i), b(i)\}}$$

其中，$a(i)$表示样本i与同簇内其他样本的平均距离，$b(i)$表示样本i与最近邻簇中所有样本的平均距离。整体轮廓系数为所有样本轮廓系数的平均值：

$$S = \frac{1}{n} \sum_{i=1}^{n} s(i)$$

聚类结果分析表明，通过肘部法则确定的最优聚类数为3，对应的轮廓系数为0.742，表明聚类效果良好。三个典型场景分别代表：场景1为低波动稳定型（占比42.3%），特征为残差波动较小，功率变化平缓；场景2为中等波动过渡型（占比35.7%），特征为残差波动适中，存在一定的功率爬坡；场景3为高波动剧烈型（占比22.0%），特征为残差波动剧烈，功率变化急剧。

每个场景的概率分布通过历史数据统计获得，为两阶段随机规划模型提供了科学的概率基础。场景概率计算公式为：

$$P_s = \frac{N_s}{N_{total}}$$

其中，$N_s$表示属于场景s的天数，$N_{total}$表示总天数。

聚类结果的有效性通过Davies-Bouldin指数进一步验证，该指数定义为：

$$DB = \frac{1}{k} \sum_{i=1}^{k} \max_{j \neq i} \left\{ \frac{\sigma_i + \sigma_j}{d(c_i, c_j)} \right\}$$

其中，$\sigma_i$表示簇i内样本到簇中心的平均距离，$d(c_i, c_j)$表示簇中心i和j之间的距离。较小的DB指数表明聚类效果更好，本研究获得的DB指数为0.523，证明了聚类方法的有效性。
