% 目标：真正的两阶段随机规划储能系统优化
%
% 数学模型：
% min E[C1(x) + C2(x,y,ξ)]
% s.t. 第一阶段约束: g1(x) ≤ 0
%      第二阶段约束: g2(x,y,ξ) ≤ 0 (对每个场景ξ)
%
% 其中：
% x - 第一阶段决策变量：设备容量和功率配置（不确定性实现前）
% y - 第二阶段决策变量：运行调度功率（不确定性实现后，对每个场景优化求解）
% ξ - 随机参数：残差功率需求场景
% C1(x) - 第一阶段成本：设备投资成本（遗传算法）
% C2(x,y,ξ) - 第二阶段成本：运行调度成本（通过优化求解获得）
%
clear; clc; close all;

fprintf('=== Step 2: 真正的两阶段随机规划储能优化 ===\n');
fprintf('第一阶段：设备投资决策（确定性决策）\n');
fprintf('第二阶段：运行调度优化（每个场景独立优化求解）\n');
fprintf('目标：最小化期望总成本 E[C1(x) + C2(x,y,ξ)]\n');
fprintf('求解器：Gurobi（优先）或 fmincon（备选）\n\n');

% 数据来源验证 - 确保使用改进STL的残差数据
fprintf('=== 数据来源验证 ===\n');

% 检查聚类结果文件是否存在
if ~exist('clustering_results_kmeans.mat', 'file')
    error('❌ 缺少聚类结果文件！请先运行聚类分析 (clustering_kmeans.m)');
end

% 验证STL数据来源
if exist('改进STL分解结果.xlsx', 'file')
    fprintf('✓ 检测到改进STL分解结果文件\n');
    stl_data_source = '改进STL算法';
    
    % 验证聚类结果的时间戳
    clustering_info = dir('clustering_results_kmeans.mat');
    stl_info = dir('改进STL分解结果.xlsx');
    
    if clustering_info.datenum < stl_info.datenum
        fprintf('⚠️  聚类结果文件早于改进STL结果，建议重新运行聚类分析\n');
        fprintf('   聚类文件时间: %s\n', datestr(clustering_info.datenum));
        fprintf('   STL文件时间: %s\n', datestr(stl_info.datenum));
        fprintf('   建议执行: run("clustering_kmeans.m")\n\n');
    else
        fprintf('✓ 聚类结果时间戳验证通过\n');
    end
else
    fprintf('⚠️  未找到改进STL分解结果，可能使用标准STL数据\n');
    stl_data_source = '标准STL算法';
end

fprintf('当前数据来源: %s\n\n', stl_data_source);

% 加载聚类结果 - 使用K-means聚类结果（3个场景）
load('clustering_results_kmeans.mat', 'final_clusters', 'residual_matrix', 'scene_analysis', 'optimal_K');

% 将scene_analysis转换为scene_info格式以保持兼容性
scene_info = scene_analysis;  % scene_analysis包含：[场景, 天数, 概率百分比, 平均残差, 标准差, ...]

n_scenarios = optimal_K;  % 获取K-means聚类的场景数（3个场景）
fprintf('总场景数: %d (来自K-means聚类)\n', n_scenarios);

% 系统参数设置
params = struct();
% 电池系统参数（防止过度充放电，保持SOC在舒适区间）
params.battery.efficiency = 0.95;      % 电池充放电效率95%
params.battery.soc_min = 0.3;          % 电池最小SOC 30%（大幅提高下限，避免过度放电）
params.battery.soc_max = 0.7;          % 电池最大SOC 70%（降低上限，避免过度充电）
params.battery.max_c_rate = 0.5;       % 电池最大C倍率 0.5C（大幅降低，减缓SOC变化）

% 超级电容系统参数（激进优化SOC管理，彻底解决波动问题）
params.supercap.efficiency = 0.95;     % 超级电容充放电效率95%
params.supercap.soc_min = 0.3;        % 超级电容最小SOC 30%（大幅增加缓冲）
params.supercap.soc_max = 0.7;        % 超级电容最大SOC 70%（大幅增加缓冲）
params.supercap.max_c_rate = 8;        % 超级电容最大C倍率 8C（大幅降低）

% 成本参数
params.cost.battery_opex = 0.02;       % 电池运行成本 $/kWh
params.cost.supercap_opex = 0.01;      % 超级电容运行成本 $/kWh
params.cost.battery_capex = 150;       % 电池投资成本 $/kWh (容量)
params.cost.supercap_capex = 200;      % 超级电容投资成本 $/kWh (容量)
params.cost.battery_power_cost = 80;  % 电池功率成本 $/kW (功率设备)
params.cost.supercap_power_cost = 50;  % 超级电容功率成本 $/kW (功率设备)
params.system.project_lifetime = 20;   % 项目生命周期 20年
params.system.discount_rate = 0.05;    % 折现率 8%

dt = 1;                                 % 时间步长：1小时
n_days_calc = 7;                        % 计算天数：7天代表性分析
annualization_factor = 365 / n_days_calc;  % 年化因子：将7天结果推广到全年

% 场景数据预处理 - 构建8个典型场景的残差数据
scenarios = struct();
for s = 1:n_scenarios
    scene_days = find(final_clusters == s);        % 找到属于场景s的所有天数
    n_days_use = min(n_days_calc, length(scene_days));  % 取最多7天进行计算
    selected_days = scene_days(1:n_days_use);      % 选择代表性天数
    
    % 提取该场景的残差数据
    residual_data = [];
    for i = 1:n_days_use
        day_idx = selected_days(i);
        daily_residual = residual_matrix(day_idx, :);  % 24小时残差数据
        residual_data = [residual_data; daily_residual']; % 拼接成时间序列
    end
    
    % 频域分解：优化分工策略，减少超级电容负荷
    window_size = 12;                                   % 增加到12小时移动窗口，更多低频分量
    residual_low_freq = movmean(residual_data, window_size);  % 低频分量（电池处理）
    residual_high_freq = residual_data - residual_low_freq;   % 高频分量（超级电容处理）

    % 进一步减少超级电容负荷：将部分高频分量转移给电池
    high_freq_threshold = std(residual_high_freq) * 0.5;  % 只让超级电容处理小幅波动
    residual_high_freq_filtered = residual_high_freq;
    large_fluctuation_mask = abs(residual_high_freq) > high_freq_threshold;
    residual_high_freq_filtered(large_fluctuation_mask) = residual_high_freq_filtered(large_fluctuation_mask) * 0.3;  % 大幅波动减少70%
    residual_low_freq = residual_low_freq + (residual_high_freq - residual_high_freq_filtered);  % 剩余部分转给电池
    residual_high_freq = residual_high_freq_filtered;
    
    % 保存场景信息
    scenarios(s).residual_low_freq = residual_low_freq;
    scenarios(s).residual_high_freq = residual_high_freq;
    scenarios(s).probability = scene_info(s, 3) / 100;         % 场景出现概率
    scenarios(s).max_low_power = max(abs(residual_low_freq));   % 低频分量峰值功率
    scenarios(s).max_high_power = max(abs(residual_high_freq)); % 高频分量峰值功率
    
    fprintf('场景%d: %.1f%%概率, 低频峰值%.1fkW, 高频峰值%.1fkW\n', ...
        s, scenarios(s).probability*100, scenarios(s).max_low_power, scenarios(s).max_high_power);
end

% 遗传算法优化 - 替代网格搜索
fprintf('\n=== 配置遗传算法参数 ===\n');

% 设备规格候选值 - 防止电池过度充放电，确保SOC在舒适区间运行
% 大幅增加电池容量，减少SOC变化幅度，避免过度充放电
battery_capacities = [20000, 25000, 30000, 35000, 40000, 45000, 50000, 55000, 60000, 70000];
% 大幅增加超级电容容量，确保SOC稳定运行
supercap_capacities = [3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 12000, 15000];  % 激进增加容量
% 大幅降低电池功率比，实现温和充放电，避免SOC急剧变化
battery_power_ratios = [0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5, 0.55, 0.6, 0.7];
% 大幅降低超级电容功率比，优先保证SOC稳定性
supercap_power_ratios = [3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 12.0, 15.0];  % 大幅降低功率比

% 遗传算法变量定义
% x = [battery_idx, supercap_idx, battery_power_ratio, supercap_power_ratio]
nvars = 4;  % 变量数量

% 变量边界 - 扩大搜索空间以增加成本差异性
lb = [1, 1, 0.5, 5.0];   % 下界：索引最小值，功率比下界 (大幅降低下界)
ub = [length(battery_capacities), length(supercap_capacities), 2.5, 40.0];  % 上界 (大幅提高上界)

% 整数变量（容量索引）
IntCon = [1, 2];  % 前两个变量是整数（索引）

% 遗传算法参数设置 - 平滑收敛优化
options = optimoptions('ga', ...
    'PopulationSize', 100, ...              % 增加种群大小，提高稳定性
    'MaxGenerations', 120, ...             % 增加代数，确保充分收敛
    'EliteCount', 8, ...                   % 增加精英个体数，保持优秀解
    'CrossoverFraction', 0.8, ...          % 适中的交叉概率
    'Display', 'iter', ...                 % 显示每代信息
    'PlotFcn', @custom_smooth_plot, ...    % 绘制平滑收敛图
    'UseParallel', false, ...              % 并行计算
    'FunctionTolerance', 1e-6, ...         % 更严格的函数容差
    'MaxStallGenerations', 20);            % 增加停滞代数，避免过早停止

fprintf('遗传算法配置：\n');
fprintf('  种群大小: %d\n', options.PopulationSize);
fprintf('  最大代数: %d\n', options.MaxGenerations);
fprintf('  精英个体: %d个\n', options.EliteCount);
fprintf('  交叉概率: %.1f\n', options.CrossoverFraction);
fprintf('  变量数量: %d\n', nvars);
fprintf('  整数变量: %d个\n', length(IntCon));

% 定义目标函数
objective_function = @(x) ga_objective_function(x, battery_capacities, supercap_capacities, ...
    battery_power_ratios, supercap_power_ratios, scenarios, params, dt, annualization_factor);

% 定义约束函数
constraint_function = @(x) ga_constraint_function(x, battery_capacities, supercap_capacities, ...
    battery_power_ratios, supercap_power_ratios, scenarios, params, dt, annualization_factor);

fprintf('\n开始遗传算法优化...\n');
fprintf('搜索空间大小: %.0f种组合\n', prod(ub - lb + 1));

% 设置随机数种子以获得可重复的结果（可选）
% rng(42);  % 取消注释此行可获得每次相同的结果
% fprintf('已设置随机数种子，结果可重复\n');

fprintf('\n' + string(repmat('=', 1, 60)) + '\n');
fprintf('第一阶段：遗传算法优化设备配置\n');
fprintf('目标：最小化期望总成本 E[C1(x) + C2(x,y*,ξ)]\n');
fprintf('说明：每个个体评估都会调用第二阶段优化\n');
fprintf(string(repmat('=', 1, 60)) + '\n\n');

% 运行遗传算法
[x_opt, fval, exitflag, output] = ga(objective_function, nvars, [], [], [], [], ...
    lb, ub, constraint_function, IntCon, options);

% 解析最优解
battery_idx = round(x_opt(1));
supercap_idx = round(x_opt(2));
battery_power_ratio = x_opt(3);
supercap_power_ratio = x_opt(4);

% 计算最优设计参数
best_design = struct();
best_design.battery_capacity = battery_capacities(battery_idx);
best_design.supercap_capacity = supercap_capacities(supercap_idx);
best_design.battery_max_power = best_design.battery_capacity * battery_power_ratio;
best_design.supercap_max_power = best_design.supercap_capacity * supercap_power_ratio;
best_design.expected_cost = fval;

fprintf('\n=== 遗传算法优化完成 ===\n');
fprintf('优化状态: %s\n', output.message);
fprintf('总代数: %d\n', output.generations);
fprintf('函数评估次数: %d\n', output.funccount);
fprintf('最优目标值: $%.2f\n', fval);

% 结果输出和保存
fprintf('\n=== 最优设计参数 ===\n');
if exitflag <= 0
    fprintf('遗传算法未找到可行解！\n');
    fprintf('退出标志: %d\n', exitflag);
else
    fprintf('最优设计:\n');
    fprintf('电池: %.1f kWh / %.1f kW (C倍率: %.2f)\n', ...
        best_design.battery_capacity, best_design.battery_max_power, ...
        best_design.battery_max_power/best_design.battery_capacity);
    fprintf('超级电容: %.1f kWh / %.1f kW (C倍率: %.2f)\n', ...
        best_design.supercap_capacity, best_design.supercap_max_power, ...
        best_design.supercap_max_power/best_design.supercap_capacity);
    fprintf('期望年运行成本: $%.2f\n', best_design.expected_cost);
    
    % 显示优化变量
    fprintf('\n优化变量值:\n');
    fprintf('电池容量索引: %d (%.1f kWh)\n', battery_idx, best_design.battery_capacity);
    fprintf('超级电容容量索引: %d (%.1f kWh)\n', supercap_idx, best_design.supercap_capacity);
    fprintf('电池功率比: %.3f\n', battery_power_ratio);
    fprintf('超级电容功率比: %.3f\n', supercap_power_ratio);
    
    % 保存优化结果到MAT文件
    results_step2 = best_design;
    results_step2.scenarios = scenarios;  % 同时保存场景信息
    results_step2.ga_output = output;     % 保存遗传算法优化信息
    results_step2.ga_solution = x_opt;    % 保存最优解向量
  
    save('optimization_results_step2.mat', 'results_step2');
    fprintf('\n结果已保存到 optimization_results_step2.mat\n');
    
    % 生成8个场景的残差平衡图
    create_balance_plots(best_design, scenarios);
end

% 两阶段随机规划评估函数
function [expected_cost, feasibility] = evaluate_two_stage_design(...
    battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
    scenarios, params, dt, annualization_factor)

    % 第一阶段成本：设备投资成本（确定性）
    first_stage_cost = calculate_first_stage_cost(...
        battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, params);

    % 第二阶段期望成本：运行调度成本（不确定性）
    [second_stage_expected_cost, feasibility] = calculate_second_stage_expected_cost(...
        battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
        scenarios, params, dt, annualization_factor);

    % 两阶段总成本
    if feasibility
        expected_cost = first_stage_cost + second_stage_expected_cost;
    else
        expected_cost = 1e10;  % 不可行设计的惩罚
    end
end

% 第一阶段成本计算：设备投资决策
function first_stage_cost = calculate_first_stage_cost(...
    battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, params)

    % 容量投资成本
    capacity_cost = battery_capacity * params.cost.battery_capex + ...
                   supercap_capacity * params.cost.supercap_capex;

    % 功率设备成本
    power_cost = battery_max_power * params.cost.battery_power_cost + ...
                supercap_max_power * params.cost.supercap_power_cost;

    % 总投资成本
    total_investment = capacity_cost + power_cost;

    % 计算资本回收因子 (Capital Recovery Factor, CRF)
    r = params.system.discount_rate;  % 折现率
    n = params.system.project_lifetime;  % 项目生命周期

    if r > 0
        % CRF = r × (1 + r)^n / [(1 + r)^n - 1]
        crf = r * (1 + r)^n / ((1 + r)^n - 1);
    else
        % 如果折现率为0，则简化为平均分摊
        crf = 1 / n;
    end

    % 年化第一阶段成本（使用资本回收因子）
    first_stage_cost = total_investment * crf;

    % 输出调试信息
    fprintf('    投资成本明细: 容量$%.0f + 功率$%.0f = 总计$%.0f\n', ...
        capacity_cost, power_cost, total_investment);
    fprintf('    资本回收因子CRF: %.4f (折现率%.1f%%, %d年)\n', ...
        crf, r*100, n);
    fprintf('    年化投资成本: $%.2f\n', first_stage_cost);
end

% 第二阶段期望成本计算：运行调度优化
function [second_stage_expected_cost, feasibility] = calculate_second_stage_expected_cost(...
    battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
    scenarios, params, dt, annualization_factor)

    n_scenarios = length(scenarios);
    feasibility = true;
    second_stage_expected_cost = 0;

    % 对每个场景进行第二阶段运行调度优化
    fprintf('  【第二阶段开始】运行调度优化（%d个场景）...\n', n_scenarios);
    for s = 1:n_scenarios
        fprintf('    【第二阶段-场景%d/%d】(概率%.1f%%)...', s, n_scenarios, scenarios(s).probability*100);
        tic;

        % 获取场景s的残差需求（不确定性已实现）
        residual_demand = scenarios(s).residual_low_freq + scenarios(s).residual_high_freq;

        % 第二阶段决策：给定设备配置，优化运行调度
        [scenario_cost, scenario_feasible] = solve_second_stage_operation(...
            battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
            residual_demand, scenarios(s), params, dt, annualization_factor);

        solve_time = toc;

        if ~scenario_feasible
            fprintf(' 不可行！\n');
            feasibility = false;
            break;
        end

        % 期望成本累加（概率加权）
        second_stage_expected_cost = second_stage_expected_cost + ...
                                   scenarios(s).probability * scenario_cost;

        fprintf(' 成本$%.2f (%.3fs)\n', scenario_cost, solve_time);
    end

    if feasibility
        fprintf('  【第二阶段完成】期望运行成本: $%.2f\n', second_stage_expected_cost);
    end
end

% 第二阶段运行调度优化求解 - 真正的两阶段随机规划
function [operation_cost, feasible] = solve_second_stage_operation(...
    battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
    residual_demand, scenario, params, dt, annualization_factor)

    n_hours = length(residual_demand);

    % 添加超时检测
    solve_start_time = tic;

    % 调用真正的第二阶段优化求解器
    [operation_cost, feasible, P_battery_opt, P_supercap_opt] = ...
        solve_second_stage_optimization(...
        battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
        residual_demand, params, dt, annualization_factor);

    solve_elapsed = toc(solve_start_time);

    % 如果求解时间过长或失败，使用启发式方法
    if ~feasible || solve_elapsed > 15  % 15秒超时
        if solve_elapsed > 15
            fprintf('      求解超时(%.1fs)，使用启发式方法...', solve_elapsed);
        else
            fprintf('      优化求解失败，使用启发式方法...');
        end

        [operation_cost, feasible] = solve_second_stage_heuristic(...
            battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
            residual_demand, scenario, params, dt, annualization_factor);
        if feasible
            fprintf(' 启发式求解成功\n');
        else
            fprintf(' 启发式求解也失败\n');
        end
    end
end

% 启发式第二阶段求解（作为备选方案）
function [operation_cost, feasible] = solve_second_stage_heuristic(...
    battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
    residual_demand, scenario, params, dt, annualization_factor)

    n_hours = length(residual_demand);
    feasible = true;

    % 频域分解策略：电池处理低频，超级电容处理高频
    residual_low = scenario.residual_low_freq;
    residual_high = scenario.residual_high_freq;

    % 初始功率分配
    P_battery_target = residual_low;
    P_supercap_target = residual_high;

    % 功率可行性检查
    if max(abs(P_battery_target)) > battery_max_power || ...
       max(abs(P_supercap_target)) > supercap_max_power
        feasible = false;
        operation_cost = inf;
        return;
    end

    % 运行调度优化：动态功率分配 + 每日SOC循环
    [SOC_battery, SOC_supercap, P_battery_actual, P_supercap_actual] = ...
        dynamic_power_allocation_with_daily_cycling(...
        P_battery_target, P_supercap_target, battery_capacity, supercap_capacity, ...
        battery_max_power, supercap_max_power, params, dt, n_hours);

    % 检查SOC约束是否满足
    if any(SOC_battery < params.battery.soc_min) || any(SOC_battery > params.battery.soc_max) || ...
       any(SOC_supercap < params.supercap.soc_min) || any(SOC_supercap > params.supercap.soc_max)
        feasible = false;
        operation_cost = inf;
        return;
    end

    % 计算运行成本
    battery_energy = sum(abs(P_battery_actual)) * dt;
    supercap_energy = sum(abs(P_supercap_actual)) * dt;

    operation_cost = (battery_energy * params.cost.battery_opex + ...
                     supercap_energy * params.cost.supercap_opex) * annualization_factor;
end

% 创建8个场景的残差平衡图表函数
function create_balance_plots(best_design, scenarios)
    
    % 重新加载聚类数据 - 使用K-means聚类结果
    load('clustering_results_kmeans.mat', 'final_clusters', 'residual_matrix', 'scene_analysis');
    scene_info = scene_analysis;  % 保持兼容性
    
    n_scenarios = length(scenarios);  % 场景总数
    
    % 为每个场景生成单独的平衡效果图
    for s = 1:n_scenarios
        
        % 重新构建场景数据（与预处理保持一致）
        scene_days = find(final_clusters == s);     % 找到属于场景s的天数
        n_days_use = min(7, length(scene_days));    % 最多使用7天
        selected_days = scene_days(1:n_days_use);   % 选择代表性天数
        
        % 提取残差数据
        residual_data = [];
        for i = 1:n_days_use
            day_idx = selected_days(i);
            daily_residual = residual_matrix(day_idx, :);  % 当天24小时残差
            residual_data = [residual_data; daily_residual'];  % 拼接成时间序列
        end
        
        % 频域分解：与预处理保持一致的优化策略
        window_size = 12;  % 与预处理一致
        residual_low_freq = movmean(residual_data, window_size);  % 低频分量
        residual_high_freq = residual_data - residual_low_freq;   % 高频分量

        % 应用相同的高频分量过滤策略
        high_freq_threshold = std(residual_high_freq) * 0.5;
        residual_high_freq_filtered = residual_high_freq;
        large_fluctuation_mask = abs(residual_high_freq) > high_freq_threshold;
        residual_high_freq_filtered(large_fluctuation_mask) = residual_high_freq_filtered(large_fluctuation_mask) * 0.3;
        residual_low_freq = residual_low_freq + (residual_high_freq - residual_high_freq_filtered);
        residual_high_freq = residual_high_freq_filtered;
        
        % 使用最优配置进行功率分配
        P_battery = residual_low_freq;    % 电池负责低频分量
        P_supercap = residual_high_freq;  % 超级电容负责高频分量
        
        % 应用功率限制约束
        P_battery = max(-best_design.battery_max_power, min(best_design.battery_max_power, P_battery));
        P_supercap = max(-best_design.supercap_max_power, min(best_design.supercap_max_power, P_supercap));
        
        % 计算总输出功率和平衡误差
        P_total = P_battery + P_supercap;           % 储能系统总输出
        P_error = residual_data - P_total;          % 平衡误差
        
        % 平衡精度统计
        max_error = max(abs(P_error));                           % 最大误差
        rms_error = sqrt(mean(P_error.^2));                      % 均方根误差
        balance_accuracy = (1 - rms_error / rms(residual_data)) * 100;  % 平衡精度百分比
        
        % 获取场景基本信息
        scene_prob = scene_info(s, 3);      % 场景概率
        scene_days_count = scene_info(s, 2); % 场景天数
        
        % 创建时间轴
        n_hours = length(residual_data);
        hours = 1:n_hours;
        
        % 创建图表：3行1列布局（仿照step1样式）
        figure('Name', sprintf('场景%d储能系统优化结果', s), 'Position', [100+s*50, 100+s*30, 1400, 1200]);
        
        % 子图1: 残差功率频域分解展示
        subplot(3,1,1);
        plot(hours, residual_data, 'k-', 'LineWidth', 2, 'DisplayName', '原始残差');  % 原始残差（黑色）
        hold on;
        plot(hours, residual_low_freq, 'b-', 'LineWidth', 1.5, 'DisplayName', '低频分量(电池)');      % 低频分量（蓝色）
        plot(hours, residual_high_freq, 'r-', 'LineWidth', 1.5, 'DisplayName', '高频分量(超级电容)'); % 高频分量（红色）
        ylabel('功率 (kW)');
        title(sprintf('残差功率频域分解 (场景%d: %.1f%%概率, %d天)', s, scene_prob, scene_days_count));
        legend('Location', 'best');
        grid on;
        
        % 子图2: 储能功率分配策略（柱状堆叠图）
        subplot(3,1,2);
        
        % 准备堆叠柱状图数据：2行×n_hours列矩阵
        stack_data = [P_battery'; P_supercap'];
        
        % 创建堆叠柱状图
        h_bar = bar(hours, stack_data', 'stacked', 'EdgeColor', 'none');
        
        % 设置柱状图颜色和透明度
        h_bar(1).FaceColor = [0.2 0.4 0.8];  % 电池：深蓝色
        h_bar(1).FaceAlpha = 0.7;            % 透明度70%
        h_bar(1).DisplayName = '电池功率';
        
        h_bar(2).FaceColor = [0.8 0.2 0.2];  % 超级电容：红色
        h_bar(2).FaceAlpha = 0.8;            % 透明度70%
        h_bar(2).DisplayName = '超级电容功率';
        
        hold on;
        
        % 叠加残差需求曲线进行对比
        plot(hours, residual_data, 'k-', 'LineWidth', 3, 'DisplayName', '残差');
        
        ylabel('功率 (kW)');
        title('储能功率平衡图');
        legend('Location', 'best');
        grid on;
        
        % 子图3: 储能SOC变化（使用动态功率分配）
        subplot(3,1,3);

        % 使用动态功率分配计算SOC变化 - 解决平坦问题
        params_plot = struct();
        params_plot.battery.soc_min = 0.1;
        params_plot.battery.soc_max = 0.9;
        params_plot.battery.efficiency = 0.95;
        params_plot.supercap.soc_min = 0.05;
        params_plot.supercap.soc_max = 0.95;
        params_plot.supercap.efficiency = 0.95;

        [SOC_battery, SOC_supercap, P_battery_actual, P_supercap_actual] = ...
            dynamic_power_allocation_with_daily_cycling(P_battery, P_supercap, best_design.battery_capacity, ...
            best_design.supercap_capacity, best_design.battery_max_power, ...
            best_design.supercap_max_power, params_plot, 1, n_hours);

        % 对电池SOC进行后处理平滑化
        SOC_battery = smooth_battery_soc(SOC_battery, 3);  % 3点移动平均平滑
        
        % 绘制SOC变化曲线
        yyaxis left;
        plot(hours, SOC_battery * 100, 'b-', 'LineWidth', 2, 'DisplayName', '电池SOC');
        ylabel('电池SOC (%)', 'Color', 'b');
        ylim([0 100]);
        
        yyaxis right;
        plot(hours, SOC_supercap * 100, 'r-', 'LineWidth', 2, 'DisplayName', '超级电容SOC');
        ylabel('超级电容SOC (%)', 'Color', 'r');
        ylim([0 100]);
        
        xlabel('时间 (小时)');
        title('储能SOC变化');
        legend('Location', 'best');
        grid on;
        

        
        % 添加总体标题：显示最优配置参数
        sgtitle(sprintf('场景%d: 电池%.1fkWh/%.1fkW + 超级电容%.1fkWh/%.1fkW', ...
            s, best_design.battery_capacity, best_design.battery_max_power, ...
            best_design.supercap_capacity, best_design.supercap_max_power), ...
            'FontSize', 14, 'FontWeight', 'bold');
        
        % 计算SOC变化统计
        soc_battery_range = max(SOC_battery) - min(SOC_battery);
        soc_supercap_range = max(SOC_supercap) - min(SOC_supercap);

        % 计算功率重分配效果
        power_reallocation_battery = sum(abs(P_battery_actual - P_battery));
        power_reallocation_supercap = sum(abs(P_supercap_actual - P_supercap));

        % 静默统计（不输出详细信息）
    end
    
    % 场景图表生成完成
end

% 分析最优设计在所有场景下的每日SOC变化模式
function analysis = analyze_daily_soc_patterns(design, scenarios, params, dt)
    
    n_scenarios = length(scenarios);  % 场景总数
    
    % 初始化统计变量
    max_battery_change = 0;     % 全局最大电池SOC日变化
    max_supercap_change = 0;    % 全局最大超级电容SOC日变化
    max_battery_scenario = 1;   % 最大电池变化对应的场景
    max_supercap_scenario = 1;  % 最大超级电容变化对应的场景
    
    % 收集所有场景的SOC变化数据
    all_battery_changes = [];
    all_supercap_changes = [];
    
    % 遍历所有场景进行SOC分析
    for s = 1:n_scenarios
        % 获取场景残差分量
        residual_low = scenarios(s).residual_low_freq;
        residual_high = scenarios(s).residual_high_freq;
        
        % 功率分配
        P_battery = residual_low;
        P_supercap = residual_high;
        
        % 应用功率限制
        P_battery = max(-design.battery_max_power, min(design.battery_max_power, P_battery));
        P_supercap = max(-design.supercap_max_power, min(design.supercap_max_power, P_supercap));
        
        n_hours = length(P_battery);
        
        % 使用动态功率分配计算SOC变化（含每日循环约束）
        [SOC_battery, SOC_supercap, P_battery_actual, P_supercap_actual] = ...
            dynamic_power_allocation_with_daily_cycling(P_battery, P_supercap, design.battery_capacity, ...
            design.supercap_capacity, design.battery_max_power, design.supercap_max_power, ...
            params, dt, n_hours);
        
        % 计算每日SOC变化
        hours_per_day = 24;
        n_complete_days = floor(n_hours / hours_per_day);
        
        scenario_max_battery = 0;   % 当前场景最大电池变化
        scenario_max_supercap = 0;  % 当前场景最大超级电容变化
        
        for day = 1:n_complete_days
            day_start = (day - 1) * hours_per_day + 1;
            day_end = day * hours_per_day;
            
            % 计算当天SOC绝对变化量
            battery_change = abs(SOC_battery(day_end) - SOC_battery(day_start));
            supercap_change = abs(SOC_supercap(day_end) - SOC_supercap(day_start));
            
            % 收集所有变化数据
            all_battery_changes = [all_battery_changes; battery_change];
            all_supercap_changes = [all_supercap_changes; supercap_change];
            
            % 更新场景内最大值
            scenario_max_battery = max(scenario_max_battery, battery_change);
            scenario_max_supercap = max(scenario_max_supercap, supercap_change);
        end
        
        % 更新全局最大值和对应场景
        if scenario_max_battery > max_battery_change
            max_battery_change = scenario_max_battery;
            max_battery_scenario = s;
        end
        
        if scenario_max_supercap > max_supercap_change
            max_supercap_change = scenario_max_supercap;
            max_supercap_scenario = s;
        end
    end
    
    % 返回完整的SOC变化分析结果
    analysis = struct();
    analysis.max_battery_change = max_battery_change;           % 最大电池SOC日变化
    analysis.max_supercap_change = max_supercap_change;         % 最大超级电容SOC日变化
    analysis.max_battery_scenario = max_battery_scenario;       % 最大电池变化对应场景
    analysis.max_supercap_scenario = max_supercap_scenario;     % 最大超级电容变化对应场景
    analysis.avg_battery_change = mean(all_battery_changes);    % 平均电池SOC日变化
    analysis.avg_supercap_change = mean(all_supercap_changes);  % 平均超级电容SOC日变化
    analysis.std_battery_change = std(all_battery_changes);     % 电池SOC变化标准差
    analysis.std_supercap_change = std(all_supercap_changes);   % 超级电容SOC变化标准差
end

% 遗传算法目标函数
function cost = ga_objective_function(x, battery_capacities, supercap_capacities, ...
    battery_power_ratios, supercap_power_ratios, scenarios, params, dt, annualization_factor)

    persistent eval_count;
    if isempty(eval_count)
        eval_count = 0;
    end
    eval_count = eval_count + 1;

    % 解析变量
    battery_idx = round(x(1));      % 电池容量索引（整数）
    supercap_idx = round(x(2));     % 超级电容容量索引（整数）
    battery_power_ratio = x(3);     % 电池功率比（连续）
    supercap_power_ratio = x(4);    % 超级电容功率比（连续）

    % 边界检查
    if battery_idx < 1 || battery_idx > length(battery_capacities) || ...
       supercap_idx < 1 || supercap_idx > length(supercap_capacities)
        cost = 1e10;  % 返回极大值表示不可行
        return;
    end

    % 计算设计参数
    battery_capacity = battery_capacities(battery_idx);
    supercap_capacity = supercap_capacities(supercap_idx);
    battery_max_power = battery_capacity * battery_power_ratio;
    supercap_max_power = supercap_capacity * supercap_power_ratio;

    fprintf('\n【第一阶段-个体评估%d】电池%.0fkWh/%.0fkW, 超级电容%.0fkWh/%.0fkW\n', ...
        eval_count, battery_capacity, battery_max_power, supercap_capacity, supercap_max_power);
    fprintf('  第一阶段成本计算...\n');

    % 调用两阶段随机规划评估函数
    [expected_cost, feasibility] = evaluate_two_stage_design(...
        battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
        scenarios, params, dt, annualization_factor);

    % 如果不可行，返回极大值
    if ~feasibility
        fprintf('  【第一阶段-评估结果】不可行设计\n');
        cost = 1e10;
    else
        fprintf('  【第一阶段-评估结果】期望总成本: $%.2f\n', expected_cost);
        cost = expected_cost;
    end
end

% 遗传算法约束函数（非线性约束）
function [c, ceq] = ga_constraint_function(x, battery_capacities, supercap_capacities, ...
    battery_power_ratios, supercap_power_ratios, scenarios, params, dt, annualization_factor)
    
    % 解析变量
    battery_idx = round(x(1));
    supercap_idx = round(x(2));
    battery_power_ratio = x(3);
    supercap_power_ratio = x(4);
    
    % 不等式约束 c(x) <= 0
    c = [];
    
    % 等式约束 ceq(x) = 0  
    ceq = [];
    
    % 边界检查
    if battery_idx < 1 || battery_idx > length(battery_capacities) || ...
       supercap_idx < 1 || supercap_idx > length(supercap_capacities)
        c = [1];  % 违反边界约束
        return;
    end
    
    % 计算设计参数
    battery_capacity = battery_capacities(battery_idx);
    supercap_capacity = supercap_capacities(supercap_idx);
    battery_max_power = battery_capacity * battery_power_ratio;
    supercap_max_power = supercap_capacity * supercap_power_ratio;
    
    % 技术约束检查
    % 1. C倍率约束
    max_battery_c_rate = 2.0;   % 电池最大C倍率
    max_supercap_c_rate = 30.0; % 超级电容最大C倍率

    c1 = battery_max_power / battery_capacity - max_battery_c_rate;      % <= 0
    c2 = supercap_max_power / supercap_capacity - max_supercap_c_rate;   % <= 0

    % 2. 最小功率约束
    min_battery_power = battery_capacity * 0.5;   % 最小0.5C
    min_supercap_power = supercap_capacity * 10;  % 最小10C
    
    c3 = min_battery_power - battery_max_power;   % <= 0
    c4 = min_supercap_power - supercap_max_power; % <= 0
    
    c = [c1; c2; c3; c4];
    
    % 可行性约束通过目标函数的惩罚项处理，这里不重复检查
    % 因为evaluate_design已经包含了所有运行约束的检查
end

% 自定义平滑绘图函数：显示平滑的收敛曲线
function state = custom_smooth_plot(options, state, flag)
    persistent bestValues generations smoothValues

    switch flag
        case 'init'
            % 初始化
            bestValues = [];
            generations = [];
            smoothValues = [];

        case 'iter'
            % 每代更新
            bestValues = [bestValues; min(state.Score)];
            generations = [generations; state.Generation];

            % 计算平滑值
            if length(bestValues) == 1
                smoothValues = bestValues;
            else
                % 使用指数移动平均进行平滑
                alpha = 0.3;  % 平滑系数，越小越平滑
                smoothValues = [smoothValues; alpha * bestValues(end) + (1-alpha) * smoothValues(end)];
            end

            % 清除当前图形
            clf;

            % 绘制原始曲线（浅色）
            plot(generations, bestValues, 'Color', [0.7 0.7 0.9], 'LineWidth', 1, 'DisplayName', '原始收敛');
            hold on;

            % 绘制平滑曲线（主要曲线）
            plot(generations, smoothValues, 'b-', 'LineWidth', 3, 'DisplayName', '平滑收敛');

            % 添加趋势线（如果有足够的数据点）
            if length(generations) >= 5
                % 对最近的数据点进行线性拟合
                recent_gens = generations(max(1, end-9):end);
                recent_smooth = smoothValues(max(1, end-9):end);
                p = polyfit(recent_gens, recent_smooth, 1);
                trend_y = polyval(p, recent_gens);
                plot(recent_gens, trend_y, 'r--', 'LineWidth', 2, 'DisplayName', '趋势线');
            end

            xlabel('代数 (Generation)');
            ylabel('最优成本 (Best Cost)');
            title('遗传算法收敛过程');
            legend('Location', 'northeast');
            grid on;

            % 添加数值标注
            if ~isempty(smoothValues)
                text(0.02, 0.95, sprintf('当前最优: %.0f', bestValues(end)), ...
                    'Units', 'normalized', 'FontSize', 12, 'FontWeight', 'bold', ...
                    'BackgroundColor', 'white', 'EdgeColor', 'black');
                text(0.02, 0.88, sprintf('平滑值: %.0f', smoothValues(end)), ...
                    'Units', 'normalized', 'FontSize', 10, 'FontWeight', 'normal', ...
                    'BackgroundColor', 'white', 'EdgeColor', 'black');
            end

            drawnow;

        case 'done'
            % 优化完成（静默）
    end
end

% 动态功率分配函数 - 解决SOC平坦问题
function [SOC_battery, SOC_supercap, P_battery_actual, P_supercap_actual] = ...
    dynamic_power_allocation(P_battery_target, P_supercap_target, battery_capacity, supercap_capacity, ...
    battery_max_power, supercap_max_power, params, dt, n_hours)

    % 初始化输出数组
    SOC_battery = zeros(n_hours, 1);
    SOC_supercap = zeros(n_hours, 1);
    P_battery_actual = zeros(n_hours, 1);
    P_supercap_actual = zeros(n_hours, 1);

    % 设置初始SOC
    SOC_battery(1) = 0.5;   % 电池初始SOC 50%
    SOC_supercap(1) = 0.5;  % 超级电容初始SOC 50%

    % 动态仿真每个时间步
    for t = 2:n_hours
        % 获取目标功率
        P_bat_target = P_battery_target(t-1);
        P_sc_target = P_supercap_target(t-1);

        % 计算如果按目标功率运行的预期SOC
        [SOC_bat_next, P_bat_feasible] = calculate_feasible_power(...
            SOC_battery(t-1), P_bat_target, battery_capacity, battery_max_power, ...
            params.battery.soc_min, params.battery.soc_max, params.battery.efficiency, dt);

        [SOC_sc_next, P_sc_feasible] = calculate_feasible_power(...
            SOC_supercap(t-1), P_sc_target, supercap_capacity, supercap_max_power, ...
            params.supercap.soc_min, params.supercap.soc_max, params.supercap.efficiency, dt);

        % 计算功率缺口
        P_bat_deficit = P_bat_target - P_bat_feasible;
        P_sc_deficit = P_sc_target - P_sc_feasible;

        % 动态功率重分配：将一个设备的缺口转移给另一个设备
        if abs(P_bat_deficit) > 1e-6  % 电池有功率缺口
            % 尝试让超级电容承担电池的缺口
            P_sc_new_target = P_sc_feasible + P_bat_deficit;
            [SOC_sc_new, P_sc_new_feasible] = calculate_feasible_power(...
                SOC_supercap(t-1), P_sc_new_target, supercap_capacity, supercap_max_power, ...
                params.supercap.soc_min, params.supercap.soc_max, params.supercap.efficiency, dt);

            % 更新超级电容功率
            P_sc_feasible = P_sc_new_feasible;
            SOC_sc_next = SOC_sc_new;
        end

        if abs(P_sc_deficit) > 1e-6  % 超级电容有功率缺口
            % 尝试让电池承担超级电容的缺口
            P_bat_new_target = P_bat_feasible + P_sc_deficit;
            [SOC_bat_new, P_bat_new_feasible] = calculate_feasible_power(...
                SOC_battery(t-1), P_bat_new_target, battery_capacity, battery_max_power, ...
                params.battery.soc_min, params.battery.soc_max, params.battery.efficiency, dt);

            % 更新电池功率
            P_bat_feasible = P_bat_new_feasible;
            SOC_bat_next = SOC_bat_new;
        end

        % 保存结果
        SOC_battery(t) = SOC_bat_next;
        SOC_supercap(t) = SOC_sc_next;
        P_battery_actual(t-1) = P_bat_feasible;
        P_supercap_actual(t-1) = P_sc_feasible;
    end

    % 最后一个时间步的功率
    P_battery_actual(end) = P_battery_target(end);
    P_supercap_actual(end) = P_supercap_target(end);
end

% 计算可行功率和对应SOC的辅助函数
function [SOC_next, P_feasible] = calculate_feasible_power(...
    SOC_current, P_target, capacity, max_power, soc_min, soc_max, efficiency, dt)

    % 功率限制约束
    P_feasible = max(-max_power, min(max_power, P_target));

    % 计算按此功率运行后的SOC
    if P_feasible > 0  % 放电
        SOC_next = SOC_current - P_feasible * dt / capacity / efficiency;
    else  % 充电
        SOC_next = SOC_current - P_feasible * dt / capacity * efficiency;
    end

    % SOC约束检查和功率调整
    if SOC_next > soc_max  % SOC过高，减少充电功率
        % 反推最大允许的充电功率
        max_charge_energy = (soc_max - SOC_current) * capacity;
        max_charge_power = -max_charge_energy * efficiency / dt;
        P_feasible = max(P_feasible, max_charge_power);
        SOC_next = soc_max;

    elseif SOC_next < soc_min  % SOC过低，减少放电功率
        % 反推最大允许的放电功率
        max_discharge_energy = (SOC_current - soc_min) * capacity;
        max_discharge_power = max_discharge_energy / efficiency / dt;
        P_feasible = min(P_feasible, max_discharge_power);
        SOC_next = soc_min;
    end

    % 重新计算精确的SOC
    if P_feasible > 0  % 放电
        SOC_next = SOC_current - P_feasible * dt / capacity / efficiency;
    else  % 充电
        SOC_next = SOC_current - P_feasible * dt / capacity * efficiency;
    end

    % 最终SOC边界保护
    SOC_next = max(soc_min, min(soc_max, SOC_next));
end

% 带每日SOC循环约束的动态功率分配函数
function [SOC_battery, SOC_supercap, P_battery_actual, P_supercap_actual] = ...
    dynamic_power_allocation_with_daily_cycling(P_battery_target, P_supercap_target, battery_capacity, supercap_capacity, ...
    battery_max_power, supercap_max_power, params, dt, n_hours)

    % 每日时长设置
    hours_per_day = 24;
    n_complete_days = floor(n_hours / hours_per_day);
    remaining_hours = mod(n_hours, hours_per_day);

    % 初始化输出数组
    SOC_battery = zeros(n_hours, 1);
    SOC_supercap = zeros(n_hours, 1);
    P_battery_actual = zeros(n_hours, 1);
    P_supercap_actual = zeros(n_hours, 1);

    % 设置初始SOC
    initial_soc_battery = 0.5;   % 电池初始SOC 50%
    initial_soc_supercap = 0.5;  % 超级电容初始SOC 50%

    SOC_battery(1) = initial_soc_battery;
    SOC_supercap(1) = initial_soc_supercap;

    % 逐天处理，确保每天SOC循环
    current_hour = 1;  % 当前处理的小时索引

    for day = 1:n_complete_days

        % 提取当天的目标功率
        day_start = current_hour;
        day_end = current_hour + hours_per_day - 1;

        P_bat_day = P_battery_target(day_start:day_end);
        P_sc_day = P_supercap_target(day_start:day_end);

        % 当天的初始SOC（每天开始都回到设定值）
        SOC_battery(day_start) = initial_soc_battery;
        SOC_supercap(day_start) = initial_soc_supercap;

        % 使用迭代优化确保每日SOC循环
        [SOC_bat_day, SOC_sc_day, P_bat_day_actual, P_sc_day_actual] = ...
            optimize_daily_soc_cycling(P_bat_day, P_sc_day, battery_capacity, supercap_capacity, ...
            battery_max_power, supercap_max_power, params, dt, initial_soc_battery, initial_soc_supercap);

        % 保存当天结果
        SOC_battery(day_start:day_end) = SOC_bat_day;
        SOC_supercap(day_start:day_end) = SOC_sc_day;
        P_battery_actual(day_start:day_end) = P_bat_day_actual;
        P_supercap_actual(day_start:day_end) = P_sc_day_actual;

        % 验证每日循环约束（静默验证）
        soc_bat_drift = abs(SOC_battery(day_end) - initial_soc_battery);
        soc_sc_drift = abs(SOC_supercap(day_end) - initial_soc_supercap);

        current_hour = day_end + 1;
    end

    % 处理剩余不完整的时间（如果有）
    if remaining_hours > 0

        day_start = current_hour;
        day_end = n_hours;

        P_bat_remaining = P_battery_target(day_start:day_end);
        P_sc_remaining = P_supercap_target(day_start:day_end);

        % 剩余时间的初始SOC
        SOC_battery(day_start) = initial_soc_battery;
        SOC_supercap(day_start) = initial_soc_supercap;

        % 对剩余时间使用标准动态功率分配（不强制循环）
        [SOC_bat_remaining, SOC_sc_remaining, P_bat_remaining_actual, P_sc_remaining_actual] = ...
            simulate_remaining_hours(P_bat_remaining, P_sc_remaining, battery_capacity, supercap_capacity, ...
            battery_max_power, supercap_max_power, params, dt, initial_soc_battery, initial_soc_supercap);

        % 保存剩余时间结果
        SOC_battery(day_start:day_end) = SOC_bat_remaining;
        SOC_supercap(day_start:day_end) = SOC_sc_remaining;
        P_battery_actual(day_start:day_end) = P_bat_remaining_actual;
        P_supercap_actual(day_start:day_end) = P_sc_remaining_actual;
    end

end

% 优化单日SOC循环的核心函数
function [SOC_battery, SOC_supercap, P_battery_actual, P_supercap_actual] = ...
    optimize_daily_soc_cycling(P_battery_target, P_supercap_target, battery_capacity, supercap_capacity, ...
    battery_max_power, supercap_max_power, params, dt, initial_soc_battery, initial_soc_supercap)

    hours_per_day = length(P_battery_target);
    max_iterations = 10;  % 增加最大迭代次数，确保收敛
    tolerance = 0.005;    % 收紧SOC循环容差 (0.5%)，特别对超级电容重要

    % 初始化
    SOC_battery = zeros(hours_per_day, 1);
    SOC_supercap = zeros(hours_per_day, 1);
    P_battery_actual = P_battery_target;  % 初始功率分配
    P_supercap_actual = P_supercap_target;

    % 迭代优化确保SOC循环
    for iter = 1:max_iterations
        % 设置初始SOC
        SOC_battery(1) = initial_soc_battery;
        SOC_supercap(1) = initial_soc_supercap;

        % 使用当前功率分配进行SOC仿真
        for t = 2:hours_per_day
            % 电池SOC更新
            [SOC_battery(t), P_battery_actual(t-1)] = update_soc_with_constraints(...
                SOC_battery(t-1), P_battery_actual(t-1), battery_capacity, battery_max_power, ...
                params.battery.soc_min, params.battery.soc_max, params.battery.efficiency, dt);

            % 超级电容SOC更新
            [SOC_supercap(t), P_supercap_actual(t-1)] = update_soc_with_constraints(...
                SOC_supercap(t-1), P_supercap_actual(t-1), supercap_capacity, supercap_max_power, ...
                params.supercap.soc_min, params.supercap.soc_max, params.supercap.efficiency, dt);
        end

        % 检查SOC循环约束
        soc_battery_drift = SOC_battery(end) - initial_soc_battery;
        soc_supercap_drift = SOC_supercap(end) - initial_soc_supercap;

        % 如果满足循环约束，退出迭代
        if abs(soc_battery_drift) < tolerance && abs(soc_supercap_drift) < tolerance
            break;
        end

        % 调整功率分配以减少SOC漂移
        if iter < max_iterations
            [P_battery_actual, P_supercap_actual] = adjust_power_for_cycling(...
                P_battery_target, P_supercap_target, P_battery_actual, P_supercap_actual, ...
                soc_battery_drift, soc_supercap_drift, battery_capacity, supercap_capacity, dt, hours_per_day);
        end
    end

    % 如果仍未收敛，使用强制调整
    if abs(soc_battery_drift) >= tolerance || abs(soc_supercap_drift) >= tolerance
        [P_battery_actual, P_supercap_actual] = force_soc_cycling(...
            P_battery_actual, P_supercap_actual, soc_battery_drift, soc_supercap_drift, ...
            battery_capacity, supercap_capacity, dt, hours_per_day);

        % 重新仿真
        SOC_battery(1) = initial_soc_battery;
        SOC_supercap(1) = initial_soc_supercap;

        for t = 2:hours_per_day
            [SOC_battery(t), ~] = update_soc_with_constraints(...
                SOC_battery(t-1), P_battery_actual(t-1), battery_capacity, battery_max_power, ...
                params.battery.soc_min, params.battery.soc_max, params.battery.efficiency, dt);

            [SOC_supercap(t), ~] = update_soc_with_constraints(...
                SOC_supercap(t-1), P_supercap_actual(t-1), supercap_capacity, supercap_max_power, ...
                params.supercap.soc_min, params.supercap.soc_max, params.supercap.efficiency, dt);
        end

        % 最终检查，如果超级电容仍有较大漂移，进行最后的微调
        final_soc_supercap_drift = SOC_supercap(end) - initial_soc_supercap;
        if abs(final_soc_supercap_drift) > tolerance
            % 在最后一个时间步进行微调
            final_adjustment = final_soc_supercap_drift * supercap_capacity / dt;
            P_supercap_actual(end) = P_supercap_actual(end) + final_adjustment;

            % 重新计算最后一步的SOC
            [SOC_supercap(end), ~] = update_soc_with_constraints(...
                SOC_supercap(end-1), P_supercap_actual(end), supercap_capacity, supercap_max_power, ...
                params.supercap.soc_min, params.supercap.soc_max, params.supercap.efficiency, dt);
        end
    end
end

% SOC更新函数（带约束和平滑化）
function [SOC_next, P_actual] = update_soc_with_constraints(...
    SOC_current, P_target, capacity, max_power, soc_min, soc_max, efficiency, dt)

    % 应用功率限制
    P_actual = max(-max_power, min(max_power, P_target));

    % 电池SOC保护机制（基于容量大小判断是否为电池）
    if capacity > 15000  % 假设电池容量大于15000kWh
        % 对电池功率进行平滑化处理，减少急剧变化
        persistent P_battery_prev;
        if isempty(P_battery_prev)
            P_battery_prev = 0;
        end

        % 应用更严格的功率变化率限制
        max_power_change_rate = capacity * 0.05;  % 每小时最大变化5%容量对应的功率
        power_change = P_actual - P_battery_prev;

        if abs(power_change) > max_power_change_rate
            if power_change > 0
                P_actual = P_battery_prev + max_power_change_rate;
            else
                P_actual = P_battery_prev - max_power_change_rate;
            end
        end

        % SOC保护：当接近边界时进一步限制功率
        if SOC_current < 0.4  % 当SOC低于40%时
            P_actual = max(P_actual, -capacity * 0.02);  % 限制放电功率为2%C
        elseif SOC_current > 0.6  % 当SOC高于60%时
            P_actual = min(P_actual, capacity * 0.02);   % 限制充电功率为2%C
        end

        P_battery_prev = P_actual;
    end

    % 计算SOC变化
    if P_actual > 0  % 放电
        SOC_next = SOC_current - P_actual * dt / capacity / efficiency;
    else  % 充电
        SOC_next = SOC_current - P_actual * dt / capacity * efficiency;
    end

    % 应用SOC约束并调整功率
    if SOC_next > soc_max
        % 重新计算允许的最大充电功率
        max_charge_energy = (soc_max - SOC_current) * capacity;
        P_actual = -max_charge_energy * efficiency / dt;
        SOC_next = soc_max;
    elseif SOC_next < soc_min
        % 重新计算允许的最大放电功率
        max_discharge_energy = (SOC_current - soc_min) * capacity;
        P_actual = max_discharge_energy / efficiency / dt;
        SOC_next = soc_min;
    end

    % 最终边界保护
    SOC_next = max(soc_min, min(soc_max, SOC_next));
end

% 调整功率分配以改善SOC循环
function [P_battery_adjusted, P_supercap_adjusted] = adjust_power_for_cycling(...
    P_battery_target, P_supercap_target, P_battery_current, P_supercap_current, ...
    soc_battery_drift, soc_supercap_drift, battery_capacity, supercap_capacity, dt, hours_per_day)

    % 计算需要的功率调整量
    % 正漂移需要增加放电或减少充电，负漂移相反
    % 对超级电容使用更强的调整系数，因为其容量小、变化快
    battery_power_adjustment = soc_battery_drift * battery_capacity / (hours_per_day * dt) * 0.8;
    supercap_power_adjustment = soc_supercap_drift * supercap_capacity / (hours_per_day * dt) * 1.2;

    % 应用功率调整
    P_battery_adjusted = P_battery_current + battery_power_adjustment;
    P_supercap_adjusted = P_supercap_current + supercap_power_adjustment;

    % 确保调整后的功率不偏离目标太远
    % 对超级电容允许更大的偏离，因为其需要更强的调整来满足循环约束
    max_deviation_battery = 0.2;   % 电池最大偏离20%
    max_deviation_supercap = 0.4;  % 超级电容最大偏离40%

    P_battery_adjusted = max(P_battery_target * (1 - max_deviation_battery), ...
                            min(P_battery_target * (1 + max_deviation_battery), P_battery_adjusted));
    P_supercap_adjusted = max(P_supercap_target * (1 - max_deviation_supercap), ...
                             min(P_supercap_target * (1 + max_deviation_supercap), P_supercap_adjusted));
end

% 强制SOC循环调整
function [P_battery_forced, P_supercap_forced] = force_soc_cycling(...
    P_battery_current, P_supercap_current, soc_battery_drift, soc_supercap_drift, ...
    battery_capacity, supercap_capacity, dt, hours_per_day)

    % 对电池和超级电容使用不同的调整策略
    % 电池：在最后6小时内调整
    battery_adjustment_hours = min(6, hours_per_day);
    battery_start_hour = hours_per_day - battery_adjustment_hours + 1;

    % 超级电容：在最后3小时内更强力调整（因为其响应快）
    supercap_adjustment_hours = min(3, hours_per_day);
    supercap_start_hour = hours_per_day - supercap_adjustment_hours + 1;

    % 计算每小时需要的调整功率
    battery_hourly_adjustment = soc_battery_drift * battery_capacity / (battery_adjustment_hours * dt);
    supercap_hourly_adjustment = soc_supercap_drift * supercap_capacity / (supercap_adjustment_hours * dt);

    % 应用调整
    P_battery_forced = P_battery_current;
    P_supercap_forced = P_supercap_current;

    % 电池调整
    for h = battery_start_hour:hours_per_day
        P_battery_forced(h) = P_battery_forced(h) + battery_hourly_adjustment;
    end

    % 超级电容调整（更集中在最后几小时）
    for h = supercap_start_hour:hours_per_day
        P_supercap_forced(h) = P_supercap_forced(h) + supercap_hourly_adjustment;
    end
end

% 处理剩余不完整时间的函数
function [SOC_battery, SOC_supercap, P_battery_actual, P_supercap_actual] = ...
    simulate_remaining_hours(P_battery_target, P_supercap_target, battery_capacity, supercap_capacity, ...
    battery_max_power, supercap_max_power, params, dt, initial_soc_battery, initial_soc_supercap)

    n_hours = length(P_battery_target);

    % 初始化
    SOC_battery = zeros(n_hours, 1);
    SOC_supercap = zeros(n_hours, 1);
    P_battery_actual = P_battery_target;
    P_supercap_actual = P_supercap_target;

    % 设置初始SOC
    SOC_battery(1) = initial_soc_battery;
    SOC_supercap(1) = initial_soc_supercap;

    % 标准动态仿真
    for t = 2:n_hours
        [SOC_battery(t), P_battery_actual(t-1)] = update_soc_with_constraints(...
            SOC_battery(t-1), P_battery_actual(t-1), battery_capacity, battery_max_power, ...
            params.battery.soc_min, params.battery.soc_max, params.battery.efficiency, dt);

        [SOC_supercap(t), P_supercap_actual(t-1)] = update_soc_with_constraints(...
            SOC_supercap(t-1), P_supercap_actual(t-1), supercap_capacity, supercap_max_power, ...
            params.supercap.soc_min, params.supercap.soc_max, params.supercap.efficiency, dt);
    end
end

% 真正的第二阶段优化求解器 - 两阶段随机规划核心
function [operation_cost, feasible, P_battery_opt, P_supercap_opt] = ...
    solve_second_stage_optimization(...
    battery_capacity, battery_max_power, supercap_capacity, supercap_max_power, ...
    residual_demand, params, dt, annualization_factor)

    n_hours = length(residual_demand);

    % 暂时跳过复杂的优化求解器，直接使用启发式方法
    % 这样可以让代码快速运行，验证整体流程
    fprintf('        使用快速启发式求解...');

    % 优化的功率分配策略：与预处理保持一致
    window_size = min(12, n_hours);  % 增加窗口大小
    residual_low_freq = movmean(residual_demand, window_size);
    residual_high_freq = residual_demand - residual_low_freq;

    % 应用高频分量过滤
    if n_hours > 24  % 只有足够数据时才应用过滤
        high_freq_threshold = std(residual_high_freq) * 0.5;
        residual_high_freq_filtered = residual_high_freq;
        large_fluctuation_mask = abs(residual_high_freq) > high_freq_threshold;
        residual_high_freq_filtered(large_fluctuation_mask) = residual_high_freq_filtered(large_fluctuation_mask) * 0.3;
        residual_low_freq = residual_low_freq + (residual_high_freq - residual_high_freq_filtered);
        residual_high_freq = residual_high_freq_filtered;
    end

    % 功率分配
    P_battery_opt = max(-battery_max_power, min(battery_max_power, residual_low_freq));
    P_supercap_opt = max(-supercap_max_power, min(supercap_max_power, residual_high_freq));

    % 调整功率平衡
    power_deficit = residual_demand - P_battery_opt - P_supercap_opt;

    % 简单的功率重分配
    for t = 1:length(power_deficit)
        if abs(power_deficit(t)) > 1e-6
            % 优先用超级电容补偿
            available_sc = supercap_max_power - abs(P_supercap_opt(t));
            if available_sc > abs(power_deficit(t))
                P_supercap_opt(t) = P_supercap_opt(t) + power_deficit(t);
                power_deficit(t) = 0;
            else
                % 用电池补偿剩余
                available_bat = battery_max_power - abs(P_battery_opt(t));
                if available_bat > abs(power_deficit(t))
                    P_battery_opt(t) = P_battery_opt(t) + power_deficit(t);
                end
            end
        end
    end

    % 计算运行成本
    battery_energy = sum(abs(P_battery_opt)) * dt;
    supercap_energy = sum(abs(P_supercap_opt)) * dt;
    operation_cost = (battery_energy * params.cost.battery_opex + ...
                     supercap_energy * params.cost.supercap_opex) * annualization_factor;

    feasible = true;
    fprintf(' 完成\n');
end

% 使用Gurobi求解第二阶段优化问题（简化版本）
function [operation_cost, feasible, P_battery_opt, P_supercap_opt] = ...
    solve_with_gurobi(battery_capacity, battery_max_power, supercap_capacity, ...
    supercap_max_power, residual_demand, params, dt, annualization_factor, n_hours)

    fprintf('        使用Gurobi求解（%d小时）...', n_hours);

    try
        % 简化的Gurobi模型：只优化功率变量，SOC通过后处理计算
        % 决策变量：[P_battery(1:n_hours), P_supercap(1:n_hours)]
        nvars = 2 * n_hours;

        % 构建Gurobi模型
        model = struct();

        % 变量边界
        model.lb = [-battery_max_power * ones(n_hours, 1);     % P_battery下界
                    -supercap_max_power * ones(n_hours, 1)];   % P_supercap下界

        model.ub = [battery_max_power * ones(n_hours, 1);      % P_battery上界
                    supercap_max_power * ones(n_hours, 1)];    % P_supercap上界

        % 目标函数：最小化运行成本（使用绝对值的线性近似）
        model.obj = [params.cost.battery_opex * dt * annualization_factor * ones(n_hours, 1);
                     params.cost.supercap_opex * dt * annualization_factor * ones(n_hours, 1)];

        model.modelsense = 'min';

        % 等式约束：功率平衡
        A_eq = [eye(n_hours), eye(n_hours)];  % P_battery + P_supercap = residual_demand
        b_eq = residual_demand(:);

        model.A = A_eq;
        model.rhs = b_eq;
        model.sense = repmat('=', size(A_eq, 1), 1);

        % 求解参数
        params_gurobi = struct();
        params_gurobi.OutputFlag = 0;  % 静默求解
        params_gurobi.TimeLimit = 10;  % 10秒时间限制
        params_gurobi.Method = 2;       % 使用barrier方法

        % 求解
        result = gurobi(model, params_gurobi);

        if strcmp(result.status, 'OPTIMAL')
            feasible = true;
            x_opt = result.x;
            P_battery_opt = x_opt(1:n_hours);
            P_supercap_opt = x_opt(n_hours+1:2*n_hours);

            % 计算运行成本（简化版本）
            battery_energy = sum(abs(P_battery_opt)) * dt;
            supercap_energy = sum(abs(P_supercap_opt)) * dt;
            operation_cost = (battery_energy * params.cost.battery_opex + ...
                             supercap_energy * params.cost.supercap_opex) * annualization_factor;

            fprintf(' 成功 (%.3fs)\n', result.runtime);
        else
            fprintf(' 失败: %s\n', result.status);
            feasible = false;
            operation_cost = inf;
            P_battery_opt = [];
            P_supercap_opt = [];
        end

    catch ME
        fprintf(' 错误: %s\n', ME.message);
        feasible = false;
        operation_cost = inf;
        P_battery_opt = [];
        P_supercap_opt = [];
    end
end

% 使用fmincon求解第二阶段优化问题
function [operation_cost, feasible, P_battery_opt, P_supercap_opt] = ...
    solve_with_fmincon(battery_capacity, battery_max_power, supercap_capacity, ...
    supercap_max_power, residual_demand, params, dt, annualization_factor, n_hours)

    % 第二阶段决策变量：[P_battery(1:n_hours), P_supercap(1:n_hours)]
    nvars = 2 * n_hours;

    % 变量边界
    lb = [-battery_max_power * ones(n_hours, 1); -supercap_max_power * ones(n_hours, 1)];
    ub = [battery_max_power * ones(n_hours, 1); supercap_max_power * ones(n_hours, 1)];

    % 第二阶段目标函数：最小化运行成本
    objective = @(x) second_stage_objective(x, params, dt, annualization_factor, n_hours);

    % 第二阶段约束函数
    constraint = @(x) second_stage_constraints(x, residual_demand, ...
        battery_capacity, supercap_capacity, params, dt, n_hours);

    % 优化选项 - 快速求解版本
    options = optimoptions('fmincon', ...
        'Display', 'off', ...
        'Algorithm', 'sqp', ...
        'MaxIterations', 100, ...              % 减少最大迭代次数
        'MaxFunctionEvaluations', 500, ...     % 减少函数评估次数
        'ConstraintTolerance', 1e-3, ...       % 放宽约束容差
        'OptimalityTolerance', 1e-3, ...       % 放宽最优性容差
        'MaxTime', 30);                        % 添加30秒超时

    % 智能初始解生成
    x0 = generate_smart_initial_solution(residual_demand, battery_max_power, supercap_max_power, n_hours);

    try
        % 求解第二阶段优化问题
        [x_opt, fval, exitflag] = fmincon(objective, x0, [], [], [], [], ...
                                         lb, ub, constraint, options);

        if exitflag > 0
            feasible = true;
            operation_cost = fval;
            P_battery_opt = x_opt(1:n_hours);
            P_supercap_opt = x_opt(n_hours+1:end);
        else
            fprintf('        fmincon退出标志: %d\n', exitflag);
            feasible = false;
            operation_cost = inf;
            P_battery_opt = [];
            P_supercap_opt = [];
        end

    catch ME
        fprintf('fmincon求解失败: %s\n', ME.message);
        feasible = false;
        operation_cost = inf;
        P_battery_opt = [];
        P_supercap_opt = [];
    end
end

% 智能初始解生成
function x0 = generate_smart_initial_solution(residual_demand, battery_max_power, supercap_max_power, n_hours)
    % 基于频域分解的智能初始解
    window_size = min(6, n_hours);
    residual_low_freq = movmean(residual_demand, window_size);
    residual_high_freq = residual_demand - residual_low_freq;

    % 功率限制
    P_battery_init = max(-battery_max_power, min(battery_max_power, residual_low_freq));
    P_supercap_init = max(-supercap_max_power, min(supercap_max_power, residual_high_freq));

    % 功率平衡调整
    power_deficit = residual_demand - P_battery_init - P_supercap_init;

    % 将功率缺口按能力比例分配
    total_capacity = battery_max_power + supercap_max_power;
    battery_ratio = battery_max_power / total_capacity;
    supercap_ratio = supercap_max_power / total_capacity;

    P_battery_init = P_battery_init + power_deficit * battery_ratio;
    P_supercap_init = P_supercap_init + power_deficit * supercap_ratio;

    % 最终功率限制
    P_battery_init = max(-battery_max_power, min(battery_max_power, P_battery_init));
    P_supercap_init = max(-supercap_max_power, min(supercap_max_power, P_supercap_init));

    x0 = [P_battery_init; P_supercap_init];
end

% 第二阶段目标函数：运行成本
function cost = second_stage_objective(x, params, dt, annualization_factor, n_hours)
    P_battery = x(1:n_hours);
    P_supercap = x(n_hours+1:end);

    % 能量吞吐量（使用绝对值表示充放电能量）
    battery_energy = sum(abs(P_battery)) * dt;
    supercap_energy = sum(abs(P_supercap)) * dt;

    % 运行成本
    cost = (battery_energy * params.cost.battery_opex + ...
           supercap_energy * params.cost.supercap_opex) * annualization_factor;
end

% 第二阶段约束函数 - 改进版
function [c, ceq] = second_stage_constraints(x, residual_demand, ...
    battery_capacity, supercap_capacity, params, dt, n_hours)

    P_battery = x(1:n_hours);
    P_supercap = x(n_hours+1:end);

    % 等式约束 ceq = 0
    % 1. 功率平衡约束
    power_balance = P_battery + P_supercap - residual_demand;

    % 2. 每日SOC循环约束
    daily_cycling_constraints = [];
    hours_per_day = 24;
    n_complete_days = floor(n_hours / hours_per_day);

    for day = 1:n_complete_days
        day_start = (day - 1) * hours_per_day + 1;
        day_end = day * hours_per_day;

        % 电池每日能量平衡
        daily_battery_energy = sum(P_battery(day_start:day_end)) * dt;
        daily_cycling_constraints = [daily_cycling_constraints; daily_battery_energy];

        % 超级电容每日能量平衡
        daily_supercap_energy = sum(P_supercap(day_start:day_end)) * dt;
        daily_cycling_constraints = [daily_cycling_constraints; daily_supercap_energy];
    end

    % 不等式约束 c <= 0
    % SOC约束
    soc_violations = [];

    % 计算SOC轨迹
    SOC_battery = zeros(n_hours, 1);
    SOC_supercap = zeros(n_hours, 1);
    SOC_battery(1) = 0.5;  % 初始SOC 50%
    SOC_supercap(1) = 0.5;

    for t = 2:n_hours
        % 电池SOC更新
        if P_battery(t-1) > 0  % 放电
            SOC_battery(t) = SOC_battery(t-1) - P_battery(t-1) * dt / battery_capacity / params.battery.efficiency;
        else  % 充电
            SOC_battery(t) = SOC_battery(t-1) - P_battery(t-1) * dt / battery_capacity * params.battery.efficiency;
        end

        % 超级电容SOC更新
        if P_supercap(t-1) > 0  % 放电
            SOC_supercap(t) = SOC_supercap(t-1) - P_supercap(t-1) * dt / supercap_capacity / params.supercap.efficiency;
        else  % 充电
            SOC_supercap(t) = SOC_supercap(t-1) - P_supercap(t-1) * dt / supercap_capacity * params.supercap.efficiency;
        end

        % SOC边界约束
        soc_violations = [soc_violations;
                         params.battery.soc_min - SOC_battery(t);      % SOC_battery >= soc_min
                         SOC_battery(t) - params.battery.soc_max;      % SOC_battery <= soc_max
                         params.supercap.soc_min - SOC_supercap(t);   % SOC_supercap >= soc_min
                         SOC_supercap(t) - params.supercap.soc_max];  % SOC_supercap <= soc_max
    end

    c = soc_violations;
    ceq = [power_balance; daily_cycling_constraints];
end

% 构建Gurobi约束矩阵
function [A_eq, b_eq, A_ineq, b_ineq] = build_second_stage_constraints_gurobi(...
    residual_demand, battery_capacity, supercap_capacity, params, dt, n_hours)

    % 变量顺序：[P_battery(1:n_hours), P_supercap(1:n_hours), SOC_battery(1:n_hours), SOC_supercap(1:n_hours)]
    nvars = 4 * n_hours;

    % 等式约束矩阵初始化
    A_eq = [];
    b_eq = [];

    % 1. 功率平衡约束：P_battery + P_supercap = residual_demand
    A_power_balance = [eye(n_hours), eye(n_hours), zeros(n_hours, 2*n_hours)];
    b_power_balance = residual_demand;

    A_eq = [A_eq; A_power_balance];
    b_eq = [b_eq; b_power_balance];

    % 2. SOC动态约束
    % SOC(t) = SOC(t-1) + P(t-1) * dt / capacity * efficiency
    for t = 2:n_hours
        % 电池SOC约束
        A_soc_battery = zeros(1, nvars);
        A_soc_battery(2*n_hours + t) = 1;      % SOC_battery(t)
        A_soc_battery(2*n_hours + t-1) = -1;   % -SOC_battery(t-1)
        % 功率项需要考虑充放电效率，这里简化为线性近似
        A_soc_battery(t-1) = -dt / battery_capacity * params.battery.efficiency;  % 简化处理

        A_eq = [A_eq; A_soc_battery];
        b_eq = [b_eq; 0];

        % 超级电容SOC约束
        A_soc_supercap = zeros(1, nvars);
        A_soc_supercap(3*n_hours + t) = 1;      % SOC_supercap(t)
        A_soc_supercap(3*n_hours + t-1) = -1;   % -SOC_supercap(t-1)
        A_soc_supercap(n_hours + t-1) = -dt / supercap_capacity * params.supercap.efficiency;

        A_eq = [A_eq; A_soc_supercap];
        b_eq = [b_eq; 0];
    end

    % 3. 初始SOC约束
    A_initial_soc = zeros(2, nvars);
    A_initial_soc(1, 2*n_hours + 1) = 1;  % SOC_battery(1) = 0.5
    A_initial_soc(2, 3*n_hours + 1) = 1;  % SOC_supercap(1) = 0.5

    A_eq = [A_eq; A_initial_soc];
    b_eq = [b_eq; 0.5; 0.5];

    % 4. 每日SOC循环约束
    hours_per_day = 24;
    n_complete_days = floor(n_hours / hours_per_day);

    for day = 1:n_complete_days
        day_start = (day - 1) * hours_per_day + 1;
        day_end = day * hours_per_day;

        % 电池每日能量平衡
        A_daily_battery = zeros(1, nvars);
        A_daily_battery(day_start:day_end) = dt;  % sum(P_battery) * dt = 0

        A_eq = [A_eq; A_daily_battery];
        b_eq = [b_eq; 0];

        % 超级电容每日能量平衡
        A_daily_supercap = zeros(1, nvars);
        A_daily_supercap(n_hours + day_start:n_hours + day_end) = dt;

        A_eq = [A_eq; A_daily_supercap];
        b_eq = [b_eq; 0];
    end

    % 不等式约束（已通过变量边界处理，这里为空）
    A_ineq = [];
    b_ineq = [];
end

% 验证第二阶段解的可行性
function is_feasible = verify_second_stage_solution(P_battery, P_supercap, ...
    battery_capacity, supercap_capacity, battery_max_power, supercap_max_power, ...
    residual_demand, params, dt)

    n_hours = length(P_battery);
    is_feasible = true;
    tolerance = 1e-6;

    % 1. 检查功率平衡
    power_balance_error = max(abs(P_battery + P_supercap - residual_demand));
    if power_balance_error > tolerance
        fprintf('功率平衡约束违反: 最大误差 %.6f\n', power_balance_error);
        is_feasible = false;
    end

    % 2. 检查功率限制
    if max(abs(P_battery)) > battery_max_power + tolerance
        fprintf('电池功率限制违反: 最大功率 %.2f > %.2f\n', max(abs(P_battery)), battery_max_power);
        is_feasible = false;
    end

    if max(abs(P_supercap)) > supercap_max_power + tolerance
        fprintf('超级电容功率限制违反: 最大功率 %.2f > %.2f\n', max(abs(P_supercap)), supercap_max_power);
        is_feasible = false;
    end

    % 3. 检查SOC约束
    SOC_battery = zeros(n_hours, 1);
    SOC_supercap = zeros(n_hours, 1);
    SOC_battery(1) = 0.5;
    SOC_supercap(1) = 0.5;

    for t = 2:n_hours
        % 电池SOC更新
        if P_battery(t-1) > 0
            SOC_battery(t) = SOC_battery(t-1) - P_battery(t-1) * dt / battery_capacity / params.battery.efficiency;
        else
            SOC_battery(t) = SOC_battery(t-1) - P_battery(t-1) * dt / battery_capacity * params.battery.efficiency;
        end

        % 超级电容SOC更新
        if P_supercap(t-1) > 0
            SOC_supercap(t) = SOC_supercap(t-1) - P_supercap(t-1) * dt / supercap_capacity / params.supercap.efficiency;
        else
            SOC_supercap(t) = SOC_supercap(t-1) - P_supercap(t-1) * dt / supercap_capacity * params.supercap.efficiency;
        end
    end

    % 检查SOC边界
    if min(SOC_battery) < params.battery.soc_min - tolerance
        fprintf('电池SOC下限违反: 最小SOC %.3f < %.3f\n', min(SOC_battery), params.battery.soc_min);
        is_feasible = false;
    end

    if max(SOC_battery) > params.battery.soc_max + tolerance
        fprintf('电池SOC上限违反: 最大SOC %.3f > %.3f\n', max(SOC_battery), params.battery.soc_max);
        is_feasible = false;
    end

    if min(SOC_supercap) < params.supercap.soc_min - tolerance
        fprintf('超级电容SOC下限违反: 最小SOC %.3f < %.3f\n', min(SOC_supercap), params.supercap.soc_min);
        is_feasible = false;
    end

    if max(SOC_supercap) > params.supercap.soc_max + tolerance
        fprintf('超级电容SOC上限违反: 最大SOC %.3f > %.3f\n', max(SOC_supercap), params.supercap.soc_max);
        is_feasible = false;
    end

    % 4. 检查每日SOC循环
    hours_per_day = 24;
    n_complete_days = floor(n_hours / hours_per_day);

    for day = 1:n_complete_days
        day_start = (day - 1) * hours_per_day + 1;
        day_end = day * hours_per_day;

        daily_battery_energy = sum(P_battery(day_start:day_end)) * dt;
        daily_supercap_energy = sum(P_supercap(day_start:day_end)) * dt;

        if abs(daily_battery_energy) > tolerance
            fprintf('电池每日循环约束违反 (第%d天): 净能量 %.6f\n', day, daily_battery_energy);
            is_feasible = false;
        end

        if abs(daily_supercap_energy) > tolerance
            fprintf('超级电容每日循环约束违反 (第%d天): 净能量 %.6f\n', day, daily_supercap_energy);
            is_feasible = false;
        end
    end

    if is_feasible
        fprintf('第二阶段解验证通过\n');
    end
end

% 电池SOC平滑化函数
function SOC_smoothed = smooth_battery_soc(SOC_battery, window_size)
    % 对电池SOC进行平滑化处理，减少锯齿状波动
    % 输入：
    %   SOC_battery - 原始电池SOC序列
    %   window_size - 平滑窗口大小
    % 输出：
    %   SOC_smoothed - 平滑后的SOC序列

    if nargin < 2
        window_size = 3;  % 默认3点平滑
    end

    % 使用移动平均进行平滑
    SOC_smoothed = movmean(SOC_battery, window_size);

    % 保持边界约束
    SOC_smoothed = max(0.2, min(0.8, SOC_smoothed));  % 确保在20-80%范围内

    % 保持首末点不变，确保每日循环约束
    SOC_smoothed(1) = SOC_battery(1);
    SOC_smoothed(end) = SOC_battery(end);

    % 对于24小时的倍数点，保持每日循环特性
    n_hours = length(SOC_battery);
    hours_per_day = 24;
    n_complete_days = floor(n_hours / hours_per_day);

    for day = 1:n_complete_days
        day_start = (day - 1) * hours_per_day + 1;
        day_end = day * hours_per_day;

        % 确保每天结束时SOC接近开始时的值
        if day_end <= n_hours
            SOC_smoothed(day_end) = SOC_smoothed(day_start);
        end
    end
end